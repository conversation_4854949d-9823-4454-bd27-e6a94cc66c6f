import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../widgets/dashboard_card.dart';
import '../widgets/tracking_controls.dart';
import '../widgets/app_list_widget.dart';
import '../widgets/statistics_chart.dart';
import '../widgets/settings_dialog.dart';
import '../widgets/connection_status_widget.dart';
import '../providers/app_providers.dart';
import '../providers/theme_provider.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';

class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> with TickerProviderStateMixin {
  late TabController _tabController;
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // Initialize connection to backend
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(timeTrackerRepositoryProvider).connect();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    ref.read(timeTrackerRepositoryProvider).disconnect();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ConnectionStatusAppBar(
        title: 'Time Tracker',
        actions: [
          IconButton(
            icon: const Icon(Icons.brightness_6),
            onPressed: () => ref.read(themeModeProvider.notifier).toggleTheme(),
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showSettingsDialog(context),
          ),
        ],
      ),
      body: _isDesktop(context) ? _buildDesktopLayout() : _buildMobileLayout(),
      bottomNavigationBar: _isDesktop(context) ? null : _buildBottomNavigation(),
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      children: [
        NavigationRail(
          selectedIndex: _selectedIndex,
          onDestinationSelected: (index) => setState(() => _selectedIndex = index),
          labelType: NavigationRailLabelType.all,
          destinations: const [
            NavigationRailDestination(
              icon: Icon(Icons.dashboard),
              label: Text('Dashboard'),
            ),
            NavigationRailDestination(
              icon: Icon(Icons.apps),
              label: Text('Apps'),
            ),
            NavigationRailDestination(
              icon: Icon(Icons.timeline),
              label: Text('Timeline'),
            ),
            NavigationRailDestination(
              icon: Icon(Icons.analytics),
              label: Text('Statistics'),
            ),
          ],
        ),
        const VerticalDivider(thickness: 1, width: 1),
        Expanded(child: _buildPageContent(_selectedIndex)),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildPageContent(0),
        _buildPageContent(1),
        _buildPageContent(2),
        _buildPageContent(3),
      ],
    );
  }

  Widget _buildPageContent(int index) {
    switch (index) {
      case 0:
        return _buildDashboard();
      case 1:
        return _buildAppsPage();
      case 2:
        return _buildTimelinePage();
      case 3:
        return _buildStatisticsPage();
      default:
        return _buildDashboard();
    }
  }

  Widget _buildDashboard() {
    final trackingStatus = ref.watch(trackingStatusProvider);
    final apps = ref.watch(appsProvider);

    return Column(
      children: [
        // Connection Status Banner
        const ConnectionStatusBanner(),

        // Main Dashboard Content
        Expanded(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(UIConstants.spacingM.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Welcome Section
                _buildWelcomeSection(),
                SizedBox(height: UIConstants.spacingL.h),

          // Tracking Status Card
          DashboardCard(
            title: 'Tracking Status',
            child: trackingStatus.when(
              data: (status) => TrackingControls(status: status),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, _) => _buildErrorState('Failed to load tracking status'),
            ),
          ),
          SizedBox(height: UIConstants.spacingM.h),

          // Quick Stats Grid
          _buildQuickStatsGrid(trackingStatus, apps),
          SizedBox(height: UIConstants.spacingM.h),

          // Recent Apps
          DashboardCard(
            title: 'Recent Apps',
            trailing: TextButton(
              onPressed: () => _tabController.animateTo(1),
              child: const Text('View All'),
            ),
            child: apps.when(
              data: (appList) => appList.isEmpty
                  ? _buildEmptyState('No apps tracked yet', 'Start tracking to see your apps here')
                  : AppListWidget(
                      apps: appList.take(5).toList(),
                      isCompact: true,
                    ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, _) => _buildErrorState('Failed to load apps'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Welcome back!',
          style: Theme.of(context).textTheme.headlineLarge?.copyWith(
            fontWeight: FontWeight.w700,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        SizedBox(height: UIConstants.spacingS.h),
        Text(
          'Track your time and boost productivity',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildQuickStatsGrid(AsyncValue trackingStatus, AsyncValue apps) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isWide = constraints.maxWidth > UIConstants.tabletBreakpoint;

        if (isWide) {
          return Row(
            children: [
              Expanded(child: _buildTotalAppsCard(apps)),
              SizedBox(width: UIConstants.spacingM.w),
              Expanded(child: _buildTodayTimeCard(trackingStatus)),
              SizedBox(width: UIConstants.spacingM.w),
              Expanded(child: _buildWeeklyGoalCard()),
            ],
          );
        } else {
          return Column(
            children: [
              Row(
                children: [
                  Expanded(child: _buildTotalAppsCard(apps)),
                  SizedBox(width: UIConstants.spacingM.w),
                  Expanded(child: _buildTodayTimeCard(trackingStatus)),
                ],
              ),
              SizedBox(height: UIConstants.spacingM.h),
              _buildWeeklyGoalCard(),
            ],
          );
        }
      },
    );
  }

  Widget _buildTotalAppsCard(AsyncValue apps) {
    return DashboardCard(
      title: 'Total Apps',
      isCompact: true,
      child: apps.when(
        data: (appList) => _buildStatDisplay(
          '${appList.length}',
          'applications tracked',
          Icons.apps,
          Theme.of(context).colorScheme.primary,
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (_, __) => _buildErrorState('Error loading apps'),
      ),
    );
  }

  Widget _buildTodayTimeCard(AsyncValue trackingStatus) {
    return DashboardCard(
      title: 'Today\'s Time',
      isCompact: true,
      child: trackingStatus.when(
        data: (status) => _buildStatDisplay(
          _formatDuration(status.currentSessionDuration),
          'time tracked today',
          Icons.timer,
          AppTheme.successColor,
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (_, __) => _buildErrorState('Error loading time'),
      ),
    );
  }

  Widget _buildWeeklyGoalCard() {
    return DashboardCard(
      title: 'Weekly Goal',
      isCompact: true,
      child: _buildStatDisplay(
        '32h',
        'of 40h goal (80%)',
        Icons.flag,
        AppTheme.warningColor,
      ),
    );
  }

  Widget _buildStatDisplay(String value, String subtitle, IconData icon, Color color) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(UIConstants.spacingM),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(UIConstants.radiusM),
          ),
          child: Icon(
            icon,
            color: color,
            size: UIConstants.iconM,
          ),
        ),
        SizedBox(width: UIConstants.spacingM.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                value,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.w700,
                  color: color,
                ),
              ),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(UIConstants.spacingXL.w),
        child: Column(
          children: [
            Icon(
              Icons.apps,
              size: UIConstants.iconXL * 1.5,
              color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.5),
            ),
            SizedBox(height: UIConstants.spacingM.h),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: UIConstants.spacingS.h),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(UIConstants.spacingM.w),
        child: Column(
          children: [
            Icon(
              Icons.error_outline,
              size: UIConstants.iconL,
              color: Theme.of(context).colorScheme.error,
            ),
            SizedBox(height: UIConstants.spacingS.h),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppsPage() {
    final apps = ref.watch(filteredAppsProvider);
    final searchState = ref.watch(appSearchProvider);

    return Column(
      children: [
        // Header Section
        Container(
          padding: EdgeInsets.all(UIConstants.spacingL.w),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Applications',
                          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        SizedBox(height: UIConstants.spacingXS.h),
                        Text(
                          'Manage and track your applications',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: () => _showAddAppDialog(context),
                    icon: const Icon(Icons.add),
                    label: const Text('Add App'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                ],
              ),
              SizedBox(height: UIConstants.spacingM.h),
              // Search and Filter Bar
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      decoration: InputDecoration(
                        hintText: 'Search applications...',
                        prefixIcon: Icon(
                          Icons.search,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        suffixIcon: searchState.searchQuery.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: () => ref.read(appSearchProvider.notifier).clearSearch(),
                              )
                            : null,
                      ),
                      onChanged: (value) => ref.read(appSearchProvider.notifier).updateSearchQuery(value),
                    ),
                  ),
                  SizedBox(width: UIConstants.spacingS.w),
                  IconButton(
                    icon: const Icon(Icons.filter_list),
                    onPressed: () => _showFilterDialog(context),
                    style: IconButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(UIConstants.radiusM),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Apps List
        Expanded(
          child: apps.when(
            data: (appList) => appList.isEmpty
                ? _buildEmptyAppsState()
                : Padding(
                    padding: EdgeInsets.all(UIConstants.spacingM.w),
                    child: AppListWidget(apps: appList),
                  ),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, _) => _buildErrorState('Failed to load applications'),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyAppsState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(UIConstants.spacingXXL.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(UIConstants.spacingXL),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.apps,
                size: UIConstants.iconXL * 2,
                color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
              ),
            ),
            SizedBox(height: UIConstants.spacingL.h),
            Text(
              'No Applications Yet',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            SizedBox(height: UIConstants.spacingM.h),
            Text(
              'Start tracking your applications to see them here.\nAdd your first app to get started!',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: UIConstants.spacingXL.h),
            ElevatedButton.icon(
              onPressed: () => _showAddAppDialog(context),
              icon: const Icon(Icons.add),
              label: const Text('Add Your First App'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                padding: EdgeInsets.symmetric(
                  horizontal: UIConstants.spacingXL,
                  vertical: UIConstants.spacingM,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimelinePage() {
    return const Center(
      child: Text('Timeline View - Coming Soon'),
    );
  }

  Widget _buildStatisticsPage() {
    final statistics = ref.watch(statisticsProvider);

    return SingleChildScrollView(
      padding: EdgeInsets.all(UIConstants.spacingM.w),
      child: Column(
        children: [
          DashboardCard(
            title: 'Usage Statistics',
            child: statistics.when(
              data: (stats) => StatisticsChart(statistics: stats),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, _) => Text('Error: $error'),
            ),
          ),
        ],
      ),
    );
  }

  Widget? _buildBottomNavigation() {
    if (_isDesktop(context)) return null;

    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: _tabController.index,
      onTap: (index) => _tabController.animateTo(index),
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.dashboard),
          label: 'Dashboard',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.apps),
          label: 'Apps',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.timeline),
          label: 'Timeline',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.analytics),
          label: 'Statistics',
        ),
      ],
    );
  }

  bool _isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width > UIConstants.tabletBreakpoint;
  }

  String _formatDuration(int seconds) {
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final secs = seconds % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else if (minutes > 0) {
      return '${minutes}m ${secs}s';
    } else {
      return '${secs}s';
    }
  }

  void _showSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const SettingsDialog(),
    );
  }

  void _showAddAppDialog(BuildContext context) {
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New App'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: 'Enter app name...',
            labelText: 'App Name',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.isNotEmpty) {
                ref.read(appsProvider.notifier).addApp(controller.text);
                Navigator.of(context).pop();
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    final searchState = ref.read(appSearchProvider);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sort & Filter'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sort by:',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: UIConstants.spacingS.h),
            ...AppSortBy.values.map((sortBy) => RadioListTile<AppSortBy>(
              title: Text(_getSortByLabel(sortBy)),
              value: sortBy,
              groupValue: searchState.sortBy,
              onChanged: (value) {
                if (value != null) {
                  ref.read(appSearchProvider.notifier).updateSortBy(value);
                }
              },
            )),
            SizedBox(height: UIConstants.spacingM.h),
            Text(
              'Order:',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: UIConstants.spacingS.h),
            ...SortOrder.values.map((order) => RadioListTile<SortOrder>(
              title: Text(order == SortOrder.ascending ? 'Ascending' : 'Descending'),
              value: order,
              groupValue: searchState.sortOrder,
              onChanged: (value) {
                if (value != null) {
                  ref.read(appSearchProvider.notifier).updateSortOrder(value);
                }
              },
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  String _getSortByLabel(AppSortBy sortBy) {
    switch (sortBy) {
      case AppSortBy.name:
        return 'Name';
      case AppSortBy.duration:
        return 'Duration';
      case AppSortBy.launches:
        return 'Launches';
      case AppSortBy.lastUsed:
        return 'Last Used';
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../providers/app_providers.dart';
import '../../core/theme/app_theme.dart';
import '../../core/constants/ui_constants.dart';

class ConnectionStatusWidget extends ConsumerWidget {
  final bool isCompact;

  const ConnectionStatusWidget({
    super.key,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectionStatus = ref.watch(connectionStatusProvider);

    if (!connectionStatus.isConnected) {
      return _buildDisconnectedStatus(context);
    }

    switch (connectionStatus.connectionType) {
      case ConnectionType.websocket:
        return _buildWebSocketStatus(context);
      case ConnectionType.http:
        return _buildHttpStatus(context);
      case ConnectionType.disconnected:
        return _buildDisconnectedStatus(context);
    }
  }

  Widget _buildWebSocketStatus(BuildContext context) {
    return _buildStatusIndicator(
      context: context,
      icon: Icons.wifi,
      label: isCompact ? 'WS' : 'Real-time',
      color: AppTheme.successColor,
      tooltip: 'Connected via WebSocket - Real-time updates active',
    );
  }

  Widget _buildHttpStatus(BuildContext context) {
    return _buildStatusIndicator(
      context: context,
      icon: Icons.sync,
      label: isCompact ? 'HTTP' : 'Polling',
      color: AppTheme.warningColor,
      tooltip: 'Connected via HTTP polling - Updates every few seconds',
    );
  }

  Widget _buildDisconnectedStatus(BuildContext context) {
    return _buildStatusIndicator(
      context: context,
      icon: Icons.wifi_off,
      label: isCompact ? 'OFF' : 'Offline',
      color: AppTheme.errorColor,
      tooltip: 'Disconnected from server',
    );
  }

  Widget _buildStatusIndicator({
    required BuildContext context,
    required IconData icon,
    required String label,
    required Color color,
    required String tooltip,
  }) {
    final indicator = Container(
      padding: EdgeInsets.symmetric(
        horizontal: isCompact ? UIConstants.spacingXS.w : UIConstants.spacingS.w,
        vertical: UIConstants.spacingXS.h,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(UIConstants.radiusS.r),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: isCompact ? UIConstants.iconXS.sp : UIConstants.iconS.sp,
            color: color,
          ),
          if (!isCompact) ...[
            SizedBox(width: UIConstants.spacingXS.w),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
                fontSize: isCompact ? 10.sp : 12.sp,
              ),
            ),
          ],
        ],
      ),
    );

    return Tooltip(
      message: tooltip,
      child: indicator,
    );
  }
}

class ConnectionStatusBanner extends ConsumerWidget {
  const ConnectionStatusBanner({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectionStatus = ref.watch(connectionStatusProvider);

    // Only show banner when disconnected or using HTTP fallback
    if (connectionStatus.isConnected && 
        connectionStatus.connectionType == ConnectionType.websocket) {
      return const SizedBox.shrink();
    }

    Color backgroundColor;
    String message;
    IconData icon;

    if (!connectionStatus.isConnected) {
      backgroundColor = AppTheme.errorColor;
      message = 'Disconnected from server. Some features may not work.';
      icon = Icons.wifi_off;
    } else {
      backgroundColor = AppTheme.warningColor;
      message = 'Using HTTP polling. Real-time updates may be delayed.';
      icon = Icons.sync;
    }

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: UIConstants.spacingM.w,
        vertical: UIConstants.spacingS.h,
      ),
      decoration: BoxDecoration(
        color: backgroundColor.withOpacity(0.1),
        border: Border(
          bottom: BorderSide(
            color: backgroundColor.withOpacity(0.3),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            size: UIConstants.iconS.sp,
            color: backgroundColor,
          ),
          SizedBox(width: UIConstants.spacingS.w),
          Expanded(
            child: Text(
              message,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: backgroundColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ConnectionStatusAppBar extends ConsumerWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;

  const ConnectionStatusAppBar({
    super.key,
    required this.title,
    this.actions,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppBar(
      title: Text(title),
      actions: [
        const ConnectionStatusWidget(isCompact: true),
        SizedBox(width: UIConstants.spacingS.w),
        ...?actions,
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

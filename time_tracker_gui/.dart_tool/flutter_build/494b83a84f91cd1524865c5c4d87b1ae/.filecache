{"version": 2, "files": [{"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/src/hct_solver.dart", "hash": "b972c32590c642256132827def0b9923"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/algorithms.dart", "hash": "0976264b99a1702a5d74e9acb841b775"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/flutter_logo.dart", "hash": "044d6bef26a97ada1d56ff6fe9b7cc14"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/pragma.dart", "hash": "871c4029c43c6dcb8ac9ba8f7799d310"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_texture_gl.h", "hash": "2ff91c9748bb7c022a2aeee861f51b81"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/async_cache.dart", "hash": "638c6d804d20c1f83790f7f10c4af408"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/resampler.dart", "hash": "cad4582fa75bf25d887c787f8bb92d04"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_json_method_codec.h", "hash": "c31254629784a8fd48a5ec671df85e64"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/binding.dart", "hash": "0a731b52181a917a08ac96b525f7d96b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/pie_chart/pie_chart_painter.dart", "hash": "33d19cae6969f4dfa07885f5ae01a408"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/snack_bar.dart", "hash": "d953dedc9eee14dfb343f4c5988840c4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/response.dart", "hash": "efbedb75be354b65520bce3f0855b8db"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/element.dart", "hash": "d414c1f995780a939e1d357efa0400a1"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/grid_paper.dart", "hash": "6aad5f436704faf509d60ddb032f41b4"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/lib/presentation/providers/settings_provider.dart", "hash": "e9b84318f56063d351135a7330ae78fb"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/sliver_fill.dart", "hash": "6987c3474a94dd1c4ff8f8540212f16b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/color_scheme.dart", "hash": "7bbb6aab4e83fc272886a39c92157201"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/alignment.dart", "hash": "bb020f793a10d8bb46c0bbc996bd0bfb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib/src/size_extension.dart", "hash": "2be0cb639a3e7f80cf320e6a37f0d1aa"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/multitap.dart", "hash": "578ff911d6e70b239fd629f5a0206fd8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/shared_preferences.dart", "hash": "698b47b813b0194cf3adacff5906a585"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file.dart", "hash": "ad139ffd36c17bbb2c069eb50b2ec5af"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/viewport.dart", "hash": "57b508bc908fd0950889e1d70ce36fdd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/common/env.dart", "hash": "f23b1cec674b4863aec7961f4a2ae758"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/bottom_sheet.dart", "hash": "5b66a624b831dd9a7c94d59aaa10f8bb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_queue.dart", "hash": "cf0f2c674cec774d8fc0990ee818316f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/base.dart", "hash": "62e6826075b4df67271133a79fc4d046"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system_entity.dart", "hash": "67918403456e9e1c17b3375ea708292c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/_web_image_info_io.dart", "hash": "e4da90bb20b3980a03665a080c87a098"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/physics/clamped_simulation.dart", "hash": "5979a1b66500c09f65550fab874ee847"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/platform_channel.dart", "hash": "78a0faeef5f0e801943acdca3f98393d"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/menu_bar_theme.dart", "hash": "e4a748e0ab7265def948ce2f5dbce86e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/lib/adapter_web_socket_channel.dart", "hash": "dfeee1386146a7ac69a643f9ebe0cf2f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/service_extensions.dart", "hash": "eb115c2e8f0ff170bf26a44efd1b5c05"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/shader_warm_up.dart", "hash": "e27d4685e9e6aa906547a77095cc1ac5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/http.dart", "hash": "d9696ef3a9cefaa6bf238175fe214b0b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/result/release_transformer.dart", "hash": "45a20da2b86984fa0b29030dd190c75d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/extensions/border_extension.dart", "hash": "f73cabf83c6d12946d68cf327b9ab70c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/animation.dart", "hash": "29a29ed9169067da757990e05a1476ee"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes.dart", "hash": "3e82e75a5b4bf22939d1937d2195a16e"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/refresh_indicator.dart", "hash": "e0b4c38191be9320c3113762d2dfebbb"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/restoration_properties.dart", "hash": "a8fdf31698b305c9fdad63aa7a990766"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/diagnostics.dart", "hash": "5d7b0ee48c302285b90443514166c2d2"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/text_span.dart", "hash": "e5163b554926dc261b556dc5d94245d2"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/slotted_render_object_widget.dart", "hash": "74708cb40b7b102b8e65ae54a0b644be"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/capabilities.dart", "hash": "5fe5b5ed3ec92338a01f24258b6070a3"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/rendering.dart", "hash": "4bd3950a0bf4a9f9b09f97594e363d36"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/badge_theme.dart", "hash": "e1a148a465b713a6366d5a22a1425926"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/listenable.dart", "hash": "a5bfe2d6591e761bf3c5dc0cd4ded99a"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/spell_check_suggestions_toolbar_layout_delegate.dart", "hash": "3405e08e614528c3c17afc561d056964"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/flavor.dart", "hash": "912b76b3e4d1ccf340ee3d2e911dfd28"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/pie_chart/pie_chart_renderer.dart", "hash": "649348f24c8e83a4f9eb3dbf1e174999"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/adaptive_text_selection_toolbar.dart", "hash": "5c96449c2a494ea8f3a50ecc3ba9af74"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/desktop_text_selection_toolbar.dart", "hash": "04c960ae6d770135bb0b6acf14b134a4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/byte_stream.dart", "hash": "c02d47d7f7e95654d3eb9b795e416dda"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/spacer.dart", "hash": "d2372e0fb5a584dcd1304d52e64d3f17"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/utils/path_drawing/dash_path.dart", "hash": "f6a28009bd3432a6696d2a01a02ac26c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/cupertino.dart", "hash": "9b83fabf1193bf4967b740dd7a2c8852"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/magnifier.dart", "hash": "52d0e96cbfe8e9c66aa40999df84bfa9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/delegate/stream_subscription.dart", "hash": "e2d2090c2a39f7902893e64150fe82b9"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/theme.dart", "hash": "7c4df8be3ef1b8c4564f6aa3c64ba65d"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/annotated_region.dart", "hash": "3bc33c65fa44a57d13430fdedef82bc2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/foundation.dart", "hash": "f594087d1804ddc538f758c0059eb6da"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/binding.dart", "hash": "2d4b5a2778f275040b5e438045607332"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/binding.dart", "hash": "530c4f96f1475cc4e4128ffedd705028"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/src/equatable_utils.dart", "hash": "bf850e483673d93e76e1fd5c69d8135a"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/drag_target.dart", "hash": "166147b7bee5919995e69f8ca3e69d17"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/notification_listener.dart", "hash": "d3b949a1e7578291493af5fd28846314"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/transitions.dart", "hash": "22ad3e3602e0fc7a63682e56a5aeaac0"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/desktop_text_selection.dart", "hash": "dd3402d5403be91584a0203364565b1b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/continuous_rectangle_border.dart", "hash": "93d025adfc0409629c51036cb0fdc085"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/family.dart", "hash": "751c8376ab9bb4a866f5db6d7e6b864b"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_binary_messenger.h", "hash": "ede736022263f14bbdff0885eb66ec86"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_tonal_spot.dart", "hash": "75f947f0ba87a0789a3ef91542bbc82c"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/lib/presentation/widgets/dashboard_card.dart", "hash": "4e2f15f42e53df484976034f5b3f0d3d"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/automatic_keep_alive.dart", "hash": "8e870f9527626d34dc675b9e28edce85"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/base/axis_chart/scale_axis.dart", "hash": "56b916b9c6777232ac754d024f5207cb"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/lib/presentation/widgets/settings_dialog.dart", "hash": "69b822f5cd7d1db07c4f8bea5ae978c1"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/animation/tween.dart", "hash": "73f043194b9c158454e55b3cafbdb395"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/semantics/debug.dart", "hash": "3fd33becc9141d8a690c4205c72c5d40"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/chunked_stream_reader.dart", "hash": "14acd577a81cd5aa871c66f430b95d97"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/list_section.dart", "hash": "1363e5e6d5efab4bae027262eff73765"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/curves.dart", "hash": "4aeb4635d84df42e6f220aba366af7d9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/characters.dart", "hash": "43268fa3ac45f3c527c72fc3822b9cb2"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/switch_theme.dart", "hash": "a88d8ea7c8c98dd1d35ad2853f04efe1"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/text_input.dart", "hash": "38c6297c7e2085554452d28299d29a09"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/src/stream_channel_controller.dart", "hash": "7eb05cf7f12f7aa86e040f415a5a43be"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_engine.h", "hash": "1b028c1fa72fa880b3f8ceb15c1c69df"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/chip.dart", "hash": "3303320b233b1ca33a9e6e8c93e2d2c9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta.dart", "hash": "aaace37762c25bcd679c2ab09129db12"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_dart_project.h", "hash": "33df0b32edf5c119fff6fafaa8359145"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/raw_keyboard_macos.dart", "hash": "f7b9c7a2d1589badb0b796029090d0d5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/noise.dart", "hash": "206b1db3ce5f7b9e5efd220712f8d391"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/list_tile_theme.dart", "hash": "d3abf203392ec29c7ebbda6b41360d2c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/recognizer.dart", "hash": "036fc28dc98388abec4456e8142c530f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/texture.dart", "hash": "7c07d5cc739ae29abcfbf6343ae84fdf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/shared_preferences_android.dart", "hash": "6f05b68df1b893e73008d1831589377c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/data_table_theme.dart", "hash": "956c84257f1efe6f10ab24f3d6702307"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/events.dart", "hash": "89aeee125822690cbd46b2ff43c76ec1"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/animated_icons/data/list_view.g.dart", "hash": "f8275b74f8f83272b8a8d1a79d5b2253"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/animated_size.dart", "hash": "ca759e06438affc7dcbdd9c4d8f0dbb2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/utils.dart", "hash": "05778db9e882b22da2f13083c9f28e0d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span.dart", "hash": "b7c2cc8260bb9ff9a961390b92e93294"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/auto_dispose.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_provider/auto_dispose.dart", "hash": "9eb3cf0f33c573aa9e8424441db78539"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector2.dart", "hash": "6a0fa6360b3aca8deb85dc7d88176eb8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_queue.dart", "hash": "d6f045db9bd5b72180157d44fee9fbfc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/list_extensions.dart", "hash": "9f8b50d98e75350b41d40fee06a9d7ed"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/ink_highlight.dart", "hash": "a9e3af96f170745db1c281777cb6bda9"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/_platform_selectable_region_context_menu_io.dart", "hash": "61af6ead2e2dc04677bcfb8c0c2104ab"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/delegate/stream_sink.dart", "hash": "ef83fcd13366d1d61c5dbb5c6aae5ead"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/time.dart", "hash": "872d879ea43b6b56c6feb519cc12d5a9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/unparsed_frame.dart", "hash": "0c30a117b0d1fd5c94980510832b81d0"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/tap_and_drag.dart", "hash": "a2f376b739fa28d7a71312ecf31d6465"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/error_helpers.dart", "hash": "39221ca00f5f1e0af7767613695bb5d2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/riverpod.dart", "hash": "9518a1e0696846221033c0434d777377"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/segmented_control.dart", "hash": "8e58a1e955460cf5a4ea1cea2b7606cf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/messages_async.g.dart", "hash": "2bd174cad1b04e4cca9ba7ac37905e5d"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/physics/simulation.dart", "hash": "0fbec63144acf1cb9e5d3a3d462e244b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/proxy_box.dart", "hash": "********************************"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/notched_shapes.dart", "hash": "b8c09bf358fcebf2f4c9214d1007536d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_subscription_transformer.dart", "hash": "9422bcb42f545a3d7fad54a0559effc2"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/ink_splash.dart", "hash": "31b0d2bf647a0ce615f4937dd5307b1c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/delegate/event_sink.dart", "hash": "acfd72852e16d10d8797be366c796133"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/blend/blend.dart", "hash": "f487ad099842793e5deeebcc3a8048cb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/link.dart", "hash": "1f334b50f4df781bbbfab857581c3540"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/dialog.dart", "hash": "17a28a030318e2c8f8fd653e0b862d50"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_standard_method_codec.h", "hash": "dd8da806243743ff943e6de062ddaa3d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/contrast/contrast.dart", "hash": "0c9bd1af5747fd55e7488c731ad32dee"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/assertions.dart", "hash": "82ea4f7076bd7e32c383a2466518b943"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/dropdown_menu.dart", "hash": "20d5458a880a0a10253cda660dbc42e5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_vibrant.dart", "hash": "5b04f80518a8417cb87a0aec07dacf4f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/thumb_painter.dart", "hash": "e37bb4fabbf2e61e9b7fbe06f5770679"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/clipboard.dart", "hash": "61137458bbcab0dfb643d5d50a5ae80f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/color_utils.dart", "hash": "0938e0447f447ceb7d16477a0213ce2c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/sliver.dart", "hash": "88dbcce51623c5bb2cbe1e4a0f80a902"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/path_provider_windows_real.dart", "hash": "43f4676f21ce5a48daf4878201eb46bb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/family.dart", "hash": "18d9d372c2f7421114cc2a2df21d8206"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/sheet.dart", "hash": "0e0b94d805e193b69802ca99d5a51b27"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/line_chart/line_chart.dart", "hash": "42abaae573170b1584dfe5267897a514"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/list_tile.dart", "hash": "837da7ede58523b5aff0ccbb40da75ba"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/tone_delta_pair.dart", "hash": "f5b38c21bf580c89610a8b58c65aae00"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/texture.dart", "hash": "cd6b036d4e6b746161846a50d182c0b5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/performance_overlay.dart", "hash": "3d892f04e5e34b591f8afa5dcbcee96d"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/input_border.dart", "hash": "2aec07fe4a1cd25aa500e5e22f365800"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/message_codecs.dart", "hash": "256d1c386e48e198e2e0a04345221477"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/physics/utils.dart", "hash": "727e4f662a828d4611c731f330a3d79a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/base.dart", "hash": "a4eb00bf15ad2af7e8ef8480d7f26a29"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/haptic_feedback.dart", "hash": "9ea1746a0f17f049b99a29f2f74e62ee"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/page.dart", "hash": "6b16a4d19243ba00762af7e39dafc177"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local.dart", "hash": "22b26473ffd350c0df39ffb8e1a4ba86"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/math_utils.dart", "hash": "e4ee21048ab83cc50d61ac3784afa9f5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/debug.dart", "hash": "51fa10cf30bde630913ff4c6e40723ba"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/typography.dart", "hash": "e892b3496135877dd5a0ea2ea2fc91e8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/shared_preferences_async_android.dart", "hash": "5cfe2d9d61584eae2e9c8e81be1dd9c5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/shape_decoration.dart", "hash": "6486bc074c81ec57bdafc82e6a64683a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/hct.dart", "hash": "596fb2e55b1ff1662e4bd67461fdc89d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/base/axis_chart/axis_chart_data.dart", "hash": "8b6d6af0953d19ff91357396eb18fa0e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/material_color_utilities.dart", "hash": "11df661a909009a918e6eec82d13e3ff"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/source_span.dart", "hash": "9f2eb24284aeaa1bacc5629ddb55b287"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/sliver_layout_builder.dart", "hash": "82d1200fedba087f85961d6b1b9332fe"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/ink_decoration.dart", "hash": "a2ab6e0f334e5a28af29766b82f7f4b0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/family.dart", "hash": "c32553850c6990014c017cc3b3024df3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/io.dart", "hash": "119ed2f372555dcadabe631a960de161"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/extensions/color_extension.dart", "hash": "5a3db8eea96d7f99fc027139279ba056"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/lib/presentation/pages/home_page.dart", "hash": "428b4d48fae805e54b0df6551cbd64be"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/change_notifier_provider.dart", "hash": "f186193f82036b24fc8379b1f332f817"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/animated_icons/data/pause_play.g.dart", "hash": "2ad27cdee5e6fe69626594543bd0e7c4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system.dart", "hash": "c23a0415bdaf55efdf69ac495da2aa9b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/platform_views.dart", "hash": "49194534260502aa020910c20fb3ad6a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/viewing_conditions.dart", "hash": "cb0d5b80330326e301ab4d49952b2f34"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/undo_manager.dart", "hash": "0821fcdff89c96a505e2d37cf1b52686"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/text_scaler.dart", "hash": "b6e992b1127f8376358e27027ea7a2ff"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/segmented_button_theme.dart", "hash": "b815d11a718e0a4d6dec5341e2af4c02"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/material_state_mixin.dart", "hash": "62cbf59e5c816c224ef5eaf803fc877b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/platform_view.dart", "hash": "72804f9d34b9a247c43d6cc575527370"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/empty_unmodifiable_set.dart", "hash": "0949b8197a6069783a78f4bb0a373fb0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_exception.dart", "hash": "c39101179f8bdf0b2116c1f40a3acc25"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/string_scanner.dart", "hash": "184d3b79d275d28cd02745b455041ee6"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/floating_action_button_location.dart", "hash": "964f3ee4853c34a4695db0c7e063eaa3"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/date_picker.dart", "hash": "8e7a18cd739e24a264facecc38379085"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/basic_types.dart", "hash": "44927d8a4e3825e7c3be0af91307d083"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_message_codec.h", "hash": "f68e09ad8fbb558e8ba442fee9dcc884"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/scroll_delegate.dart", "hash": "e78589269f033237f43ffdc87adc47a9"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/navigator_pop_handler.dart", "hash": "0d1b13fd16692571d5725f164d0964ef"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_binary_codec.h", "hash": "5997440e26e079541337c498925226f1"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/animated_icons/data/event_add.g.dart", "hash": "7bd8137185bc07516a1869d2065efe0d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/ascii_glyph_set.dart", "hash": "7050c8c94b55eb51260ca54708b460fa"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_map.dart", "hash": "13c9680b76d03cbd8c23463259d8deb1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/plane.dart", "hash": "f0c6d5d05fbdc95ab84f1a63894b7be6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/base/axis_chart/axis_chart_extensions.dart", "hash": "3d2796b459c4d34219ea679827e92e5b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_expressive.dart", "hash": "be096140df774ec827218c6fe69b80e5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/basic.dart", "hash": "31db92b0b980a193d02b613bb9c0f819"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/will_pop_scope.dart", "hash": "777aca422776ac8e4455ccc7958f7972"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/animated_cross_fade.dart", "hash": "98772211ffa69a8340f8088cd7193398"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/image.dart", "hash": "caad40ad1936874ea93473b300bb909c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/gestures.dart", "hash": "55324926e0669ca7d823f6e2308d4a90"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wsmeans.dart", "hash": "6c6dfd5ba4546c1f32201555d6cff215"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/bar_chart/bar_chart_data.dart", "hash": "ad73ed2fd21da87dc63eca9677f9cee2"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/star_border.dart", "hash": "e324dd19cc02a1bf47bf7cc545dcca79"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar_button.dart", "hash": "bce1e8ef07d9830bbf99031d77e0b9fc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/extensions/edge_insets_extension.dart", "hash": "ee49bdaba1ec44edd11fb9b0d8af5552"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer.dart", "hash": "db799bf48af97b7c0edc93ad96b4a6da"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/framework.dart", "hash": "d63ca0c723f6a99572c806b4ec989036"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_message_codec.h", "hash": "f68e09ad8fbb558e8ba442fee9dcc884"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/heroes.dart", "hash": "fc0b4ef021be19542435a86743d8de7c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/text_boundary.dart", "hash": "501bafdb6d3784f18f395d40dfa73cd2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/result.dart", "hash": "1325fce32c39a3792e3eeab612f942f1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/highlighter.dart", "hash": "5265b4bdec5c90bfd2937f140f3ba8fc"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/observer_list.dart", "hash": "8ae04de7c196b60c50174800d036642f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/basic_types.dart", "hash": "785eedcc96fa6a4fcc7c81a8736a7427"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/lsq_solver.dart", "hash": "d0ab7f5e11e48788c09b0d28a0376d80"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/tab_indicator.dart", "hash": "ecc072620f2a72e685360292690c8a68"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/auto_dispose_family.dart", "hash": "0dd5377006ddfc0b63c193276ef02d43"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/decoder.dart", "hash": "e6069a6342a49cdb410fbccfbe4e8557"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/custom_layout.dart", "hash": "dc552952c58db02409090792aeebbdd8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_response.dart", "hash": "358495c0e2a26e0203cd810f7ca85795"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/consolidate_response.dart", "hash": "04451542afc67a74282bd56d7ee454f5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/spell_check.dart", "hash": "e3d917994e875601c2dadaf62de546f2"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/service_extensions.dart", "hash": "d7a6c07c0b77c6d7e5f71ff3d28b86bd"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/unique_widget.dart", "hash": "11b4d96c7383b017773d65cb2843d887"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/scroll_position_with_single_context.dart", "hash": "56a764067b45a1a7cb6b7f186f54e43a"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/timeline.dart", "hash": "2fbba4502156d66db0a739144ccce9a0"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/sliver.dart", "hash": "2fe7a1026669f97031a83f6da44d248b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/isolates.dart", "hash": "1dab3723527db6a19410ed34b6acaeed"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/lib/web_socket.dart", "hash": "ae79620b36c1d588cb91f194bde8610c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/text_formatter.dart", "hash": "b139a58dace0b9d9a07a3523ed72ced5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/border_radius.dart", "hash": "3cb88cf9e4198e0d510b78aa005aa597"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/scroll_activity.dart", "hash": "ca3df05f249dbc5a38ebb86ee9a74a1e"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/semantics.dart", "hash": "4b784d6e4f290bd6d5a1f38bfb5701d8"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/performance_overlay.dart", "hash": "c5e44030289c2c25b26c5b3aa843b3cc"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/animation/listener_helpers.dart", "hash": "72bbc3da5da130fb11bb5fc65614653c"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/lib/data/repositories/mock_time_tracker_repository.dart", "hash": "cd6a1274a03c0fc1171df848dbad9823"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_plugin_registrar.h", "hash": "3d850b5432140f7e565baa806ee1f716"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/scroll_configuration.dart", "hash": "e01f6851d87ad96cbdafcbfd282517e6"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/picker.dart", "hash": "4d8781c671b7df5aadf2331931458cfb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix3.dart", "hash": "447b270ddd29fa75f44c389fee5cadd1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/extensions/rrect_extension.dart", "hash": "bd6edf459ed2affde49bfdedff60fe42"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_plugin_registry.h", "hash": "9093dc992b7f0c62c899bb5ee75437bb"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/icon_data.dart", "hash": "eb9b3bf513b18ddaf0057f3877439d9b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/utils/utils.dart", "hash": "40418177a949a2b4d4bfab08f87ae9bb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/triangle.dart", "hash": "7d2bdb4801fc8b3a110f36d5e5fa59f5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/card_theme.dart", "hash": "5d8e29422039d9dcce6908b427814d80"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/raw_keyboard_ios.dart", "hash": "1303bc77ad63625069f2d23afc73f523"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha256.dart", "hash": "1b2339e719143f3b365a03c739ab3916"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/oval_border.dart", "hash": "c8a14f8ecb364849dcdd8c67e1299fb3"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/scroll_aware_image_provider.dart", "hash": "d390b15ecef4289db88a4545e359bc8a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file.dart", "hash": "58edba46526a108c44da7a0d3ef3a6aa"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/progress_indicator.dart", "hash": "9796b800122953ccb2c3f40ba2120a94"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/material_button.dart", "hash": "c165bb259eb18a2dc493a0e7a1d1ebd9"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/tab_view.dart", "hash": "8b15d222f5742b46bf55a4ef4cbfd6e0"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/layer.dart", "hash": "cb45dd3f32378f0acf6b8a514cdc6084"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/filled_button_theme.dart", "hash": "52beedf1f39de08817236aaa2a8d28c5"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_value.h", "hash": "0fad916a7405474cce0f6d85509a12ac"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fidelity.dart", "hash": "553c5e7dc9700c1fa053cd78c1dcd60a"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/stepper.dart", "hash": "3d27bed38f1893769396b5d23f94f15e"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/back_button.dart", "hash": "035b8d3642fa73c21eafbee7851cc85d"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/chip_theme.dart", "hash": "525e57b6ade38da2132c8ddb0ea78547"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/arena.dart", "hash": "04f3f5a6ad35c823aef3b3033dc66c3c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/carousel.dart", "hash": "006c00513de6bd421565ec6ffd776337"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib/src/screenutil_mixin.dart", "hash": "4a394a54b8e7be1d7c9df7f10c89cd05"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/hit_test.dart", "hash": "2d3948bf5dd7b63d100270fce62fa2d9"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/debug_overflow_indicator.dart", "hash": "deedcf7ee9b4e76191202e61654f9dcb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/base/axis_chart/side_titles/side_titles_flex.dart", "hash": "74c234daeb81d56ee7596c93001202b9"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/text.dart", "hash": "955794ab8f9f2f33f660998c73ac222f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/navigation_bar.dart", "hash": "2539eaeb4e2f2f69f678fd850c2332e8"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/image_filter.dart", "hash": "6c0e97a3b04c9819fe935659014f92e8"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/bottom_navigation_bar.dart", "hash": "ccb3c80f13485133893f760c837c8b62"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/debug.dart", "hash": "fab9f5f0fb3bdd9295e12a17fef271c1"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/asset_bundle.dart", "hash": "ef24f0630061f35a282b177d372c00d1"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/animated_icons/data/add_event.g.dart", "hash": "a79a6f9bb06c7d6dc5fb74ac53dce31b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/time_picker.dart", "hash": "45beeaf92542183f39c458a87dcc81f7"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/lib/main.dart", "hash": "5cc59eea317ed3277b6deca3dc539315"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_exception.dart", "hash": "b062a8e2dade00779072d1c37846d161"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/media_query.dart", "hash": "98cd866294c42f2faff3451e5ca74bfa"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_random_access_file.dart", "hash": "8584e5707c45dd6bdd567a10dfd8cd0d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/always_alive.dart", "hash": "ae4469331dace367e6fb978dd7b7737e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/extensions/path_extension.dart", "hash": "b13faf802386f562057b4179e7ec9f46"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/search_bar_theme.dart", "hash": "055a5c4a10cb9bc9f1e77c2c00e4ef9a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/base/line.dart", "hash": "6ee5fd030044f9ec87835e34b09f7755"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/animated_icons/data/arrow_menu.g.dart", "hash": "555fcdeebbe6517cde1cdd95133cabd7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/platform.dart", "hash": "d2bab4c7d26ccfe4608fe8b47dd3b75c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/selection_container.dart", "hash": "97359ca5bc2635f947e7616f792565c6"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/binary_messenger.dart", "hash": "056355e344c26558a3591f2f8574e4e5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/editable_text.dart", "hash": "4eb84c94445470d8bb6bb8e2666aa51a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_devtools_extension_data.dart", "hash": "3f47c1f73c7a4541f98163b83d056456"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/line_chart/line_chart_renderer.dart", "hash": "9b7b81e6a32e86352a65cd085163a699"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash_sink.dart", "hash": "ec5409b8e30f22b65a7eee1b00a12d06"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/animated_icons/data/view_list.g.dart", "hash": "e5b4b18b359c9703926f723a1b8dd4ac"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider_lab.dart", "hash": "6566a35ff0dea9376debf257bdb08fba"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/date.dart", "hash": "86b720af61fd71f6566c9e8d42412e85"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_method_codec.h", "hash": "ea928ca76b491e0a1313d69a25103ee3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/bar_chart/bar_chart_renderer.dart", "hash": "b5e2be09469a1bf53cad57846a6cbc62"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/persistent_hash_map.dart", "hash": "7e0e723348daf7abfd74287e07b76dd8"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/bottom_app_bar_theme.dart", "hash": "ff2b2e7159e19374f968cf529da25c01"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/card.dart", "hash": "90d9d45eef80ac53b194a71da4e10975"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/pointer_signal_resolver.dart", "hash": "28d3a26c44687480bac3f72c07233bf6"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/spell_check_suggestions_toolbar.dart", "hash": "12120b49ba363d4c964cf1d043a0aa1b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib/src/_flutter_widgets.dart", "hash": "26b949575b3894692410f61281c24387"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/theme.dart", "hash": "a02235e1a98989d6740067da46b4f73d"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/inherited_notifier.dart", "hash": "12143f732513790cd579481704256dcd"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/icudtl.dat", "hash": "692337664e861ad322138061132dddc6"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/checkbox.dart", "hash": "********************************"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/system_channels.dart", "hash": "ffc66c213d3e015ff3e03298622c7141"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/provider.dart", "hash": "d5b1d01f918c452585a990bba4c2b919"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/family.dart", "hash": "9dcc50108fd667c7744d5bba6b51e1b4"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/widget_inspector.dart", "hash": "dcb5ce635282f4390eca8dcb73737991"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/raw_keyboard_fuchsia.dart", "hash": "a06bb87266e0bac30a263d7182aaf68c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/long_press.dart", "hash": "c97a8ffd51479d05a18a54ac27ccba15"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/semantics/semantics_service.dart", "hash": "48b13baf494b39e894252da0a0f6e8c0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/async_selector.dart", "hash": "c050fb9d5c851547735cf2c46d8b6288"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/node.dart", "hash": "a5d0509a39803ffb48cae2803cd4f4bd"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/router.dart", "hash": "a89f6417642d57961ee87743be4a6a2b"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_binary_messenger.h", "hash": "ede736022263f14bbdff0885eb66ec86"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/wrap.dart", "hash": "b656f459fa4dd04f817455858d3dd20f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/image_provider.dart", "hash": "25b96e83b1368abc11d4aeae19e9f398"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib/src/r_sizedbox.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/src/delegating_stream_channel.dart", "hash": "d731e1b690975788d014e6df127b2a9a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/flutter_riverpod.dart", "hash": "05100b6f82b19ef0bab59f9f174ad39e"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/nav_bar.dart", "hash": "13b920f66eba39405ab6c5487e5fc3f5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/spell_check.dart", "hash": "24094ce9de1b9222a8d6548d3c01045a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/base/base_chart/base_chart_painter.dart", "hash": "add3252f57822c109e3f76ecf55f5fdf"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/collection.dart", "hash": "4ba0a4163d73b3df00db62013fb0604e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_color.dart", "hash": "7ffb6e525c28a185f737e3e6f198f694"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/flutter_linux.h", "hash": "1dcce1af8eb3052e29d06a756f0535db"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/search.dart", "hash": "66a927b3f610db5ff8c77a6ba3ccee0b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/directory.dart", "hash": "8f4de032f1e2670ca51ce330a4de91a3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/constants.dart", "hash": "aa4b5c0cdb6a66685350611b29ca9d38"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_celebi.dart", "hash": "f12f9a9b8bb504f4617bfd1c00d403f0"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/shared_app_data.dart", "hash": "feacc941aea1ec8b3a30601915b7d353"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/variant.dart", "hash": "8dea906a9b8773920b6d1ccea59807bf"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/list_wheel_scroll_view.dart", "hash": "05c5ca73bc4e912f53a324cfa508bbfe"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/date_picker.dart", "hash": "2627dee7fb363a5bb1cbc919699bcc84"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/outlined_button_theme.dart", "hash": "8ece5be4aa5c8fa615288c4c8c5277a2"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/material_state.dart", "hash": "245a31a30063b63cbfd631fdc2ddf0d8"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/menu_style.dart", "hash": "e79db1a382e61436ed81f9f47dc06d7a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/windows.dart", "hash": "0d86d4ba2e01e5e62f80fcf3e872f561"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/functions.dart", "hash": "41f7bdb7d1eb3c86c21489902221b859"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/queue_list.dart", "hash": "02139a0e85c6b42bceaf3377d2aee3de"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/decorated_sliver.dart", "hash": "cd7f8dc942f5138db121aabbaba920ac"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/extensions/gradient_extension.dart", "hash": "7ca30234170a525ceb3dc97c2cedefcc"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/expansion_panel.dart", "hash": "5cedacfe2fd447a541cd599bfc1aef91"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_map.dart", "hash": "700328ab0177ddfd9a003a8c15619c1a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/base/base_chart/base_chart_data.dart", "hash": "15863a82b0b3fd89ccf83bde2421f306"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/future_provider/base.dart", "hash": "d7d24730943cbf47d39aa11425ebf344"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/spell_check_suggestions_toolbar.dart", "hash": "e4c4603e78131a8bc950a8029d624a76"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash.dart", "hash": "4af79c5c69ccf0cae6ab710dfb84b125"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/delegate/sink.dart", "hash": "87e6007f2e4468fd84513f05cafcca2d"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_event_channel.h", "hash": "defbac680fc3c78d98ef61319ff0dbb6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/shared_preferences_foundation.dart", "hash": "b72ebe27944e3a75601e56579bb92907"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_sink_transformer/reject_errors.dart", "hash": "2f711a88a049130159adb3f7867423c0"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/system_chrome.dart", "hash": "5638f5f2028c522b32626825f6bd5b7e"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/expansion_tile_theme.dart", "hash": "045c779ec8564825d7f11fbbd6fb2fa1"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/dropdown_menu_theme.dart", "hash": "93c17b2980fc5498f3ba266f24c6b93b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/shared_preferences_linux.dart", "hash": "492280af61b4bca29e21d28db0c2be1c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/md5.dart", "hash": "0981c95a357b5cebc932250a5e6c988e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/opengl.dart", "hash": "9e22ead5e19c7b5da6de0678c8c13dca"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/folders.dart", "hash": "4bd805daf5d0a52cb80a5ff67f37d1fd"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/snack_bar_theme.dart", "hash": "951bd729c13e8dd03a7f4edd8b10c06d"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/app_bar.dart", "hash": "22cb97b7d09f329bab7ed148b4d181e4"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/tap_region.dart", "hash": "96b4be28e9cb48156c65de35d7ccefba"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/sliver_multi_box_adaptor.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/testing/fake_platform.dart", "hash": "f1a57183b9d9b863c00fcad39308d4c1"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/keyboard_listener.dart", "hash": "bd3f0349089d88d3cd79ffed23e9163b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/motion.dart", "hash": "505f6c9750f9390c9e9e4d881092cef4"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/box_shadow.dart", "hash": "********************************"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/scheduler/service_extensions.dart", "hash": "f49291d1bc73b109df4c162db10003d2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/pie_chart/pie_chart_helper.dart", "hash": "d53e5e29157046a01f222df89f73a1e5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/progress_indicator_theme.dart", "hash": "ec2260a55dbb3ff283297d9da97e130c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/strings.dart", "hash": "4e96c754178f24bd4f6b2c16e77b3a21"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_sink_transformer/typed.dart", "hash": "35c9371cbb421753e99a2ca329107309"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/animated_icons.dart", "hash": "78ce7527fa364df47ba0e611f4531c2c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/physics/friction_simulation.dart", "hash": "732535ba697d95c80d1215c0879477f1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme.dart", "hash": "a6adbe3868e017441360895c35fd6aa2"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/mouse_cursor.dart", "hash": "bef4f4d150af7d7e46b13da4847f86fa"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/line_chart/line_chart_painter.dart", "hash": "af3972068ad1bc23dc48f1d1d720b50b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/app.dart", "hash": "66bb0d42812dbdcb77a351f5d79c74a4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id.dart", "hash": "32f5f78e5648f98d8b602c6233aa4fc5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/drag_details.dart", "hash": "f350db07fdddbcfd71c7972bf3d13488"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/calendar_date_picker.dart", "hash": "95f488b1875988eb094e0ba71deb7deb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style.dart", "hash": "bfb39b98783e4013d9fe5006de40874d"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/badge.dart", "hash": "cd7cadd0efa83f26d401a14e53964fd4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/score/score.dart", "hash": "58b9bc8a40fd3e2f7d9d380d0c2d420f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/radar_chart/radar_chart_painter.dart", "hash": "c387dd73764ee020089d98dd84249b05"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/data_table_source.dart", "hash": "094b2c03ad4e0ef5bc1144e281142b2e"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/data_table.dart", "hash": "a732cf9cb336d70db5c1145f0e468953"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_client.dart", "hash": "e4823f5eb1dffcf1cf47a9d667c5cb18"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/sliver_tree.dart", "hash": "2cb8483d7aa2b998d4641e25a0425f67"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/no_splash.dart", "hash": "9c053b0efcabd70996cc27e9d6c9303e"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/page_transitions_theme.dart", "hash": "1ed34d373b037c1696e90bf7e4f249ba"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/framework.dart", "hash": "625b858bd9847eab75d2f3f6295a25bc"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/navigation_drawer_theme.dart", "hash": "f6d18a38c0986111a3d297424ed6fbcb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/breaks.dart", "hash": "73189b511058625710f6e09c425c4278"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_basic_message_channel.h", "hash": "6cba49677d30f16cadf943f2648123fc"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/multidrag.dart", "hash": "9a977b88944bf59512e9d8aaeef93605"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/stream_channel.dart", "hash": "b399357b285dbe1fc8e858ef3b8d20e1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/proxy_provider_listenable.dart", "hash": "2e59aadb17c005953c2accd529aced98"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_sink_transformer.dart", "hash": "8117e1fa6d39c6beca7169c752319c20"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/message_codec.dart", "hash": "bf50f61746b9744a0e2d45a88815288f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/debug.dart", "hash": "6f516ffde1d36f8f5e8806e7811b15ba"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/scroll_physics.dart", "hash": "f26f519ea124441ec71b37df7cfa1ee9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/src/stream_channel_transformer.dart", "hash": "bf9deb76f520208269fedb1ee05992bc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quad.dart", "hash": "739bb2e85022ddfb653590b93216942a"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/sliver_group.dart", "hash": "ba2f8adc4e6c096b09aac919580fffee"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/eager_span_scanner.dart", "hash": "bdc22e9e77382045196b5aafd42b5e55"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/builders.dart", "hash": "ccd0c138d8f151e1ccec18f4ceb98f01"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/visibility.dart", "hash": "94dab76e00a7b1155b15796b87ebe506"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/decorated_sliver.dart", "hash": "4b50828d394e7fe1a1198468175270d9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/lib/state_notifier.dart", "hash": "5bc3c944f62b4cf5d382a0c0e9b7e09e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/subscription_stream.dart", "hash": "b637f236939a0af5ddf1bae124669288"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/intersection_result.dart", "hash": "789e79772bba1132b3efdb60636a3ccb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/src/disconnector.dart", "hash": "7732b755777af7281039ae8c5cb3eb78"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/decoration.dart", "hash": "ae85856265742b6237ed0cb67c4364af"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/lib/data/services/websocket_service.dart", "hash": "32e672f32a223b9de9ff157e89a6b66c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/url.dart", "hash": "13c8dcc201f970674db72fbbd0505581"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/text_selection.dart", "hash": "0c38ab3123facc4ec6f01ba31158c3ec"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_link.dart", "hash": "733eb3422250897324028933a5d23753"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector3.dart", "hash": "478e1071c9f577b6cabb8d72c36de077"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/animated_icons/data/menu_arrow.g.dart", "hash": "b1bb8356cca8b86afca314ab4898a527"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/debug.dart", "hash": "59cca02e92c0ff79aac0c54c50e3bd2b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/localizations.dart", "hash": "d9a659644f1b667686f2c9b22545dc0e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system_entity.dart", "hash": "22f170a8dc9abfac2942555e83589e1a"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_json_message_codec.h", "hash": "1fa21d2586a24da4ab78ae2936263c7f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/_capabilities_io.dart", "hash": "faf4d014b3617ede3150f80eba25e3b4"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/button_theme.dart", "hash": "7b0e6dd1794be4b575ecf8af6475f0e7"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/scrollbar.dart", "hash": "c86a43bc5abf7528416982490b4c0b8d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/lazy_trace.dart", "hash": "d75954340a0c7770eb9a149f7994598e"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/rotated_box.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/internals.dart", "hash": "5c4a5af039aad32f5ac9bdbfc1536af4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_data.dart", "hash": "b9abba31a48a9c2caee10ef52c5c1d0e"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/table.dart", "hash": "fad2940dc1f4f3e4a0ebb5c7ff40a3a7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_content.dart", "hash": "78e53d9a4963c0d19c5ea355a0946e5d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file_io.dart", "hash": "8830333c78de58ad9df05d396b651ef7"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/icon.dart", "hash": "826b67d0d6c27e72e7b0f702d02afcec"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/focus_manager.dart", "hash": "737642bf1a2d9ebd63c82016292b6b93"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/constants.dart", "hash": "c7cc72c1e40d30770550bfc16b13ef40"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/glyph_set.dart", "hash": "62d88517fa4f29f5f3bcec07ba6e1b62"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/raw_keyboard_windows.dart", "hash": "266a40131c9f05494e82934fd7096ed0"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/elevated_button.dart", "hash": "c2dcf2bcdc85d007f9729621d13cccf4"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/binding.dart", "hash": "15059e9824dd4a9e06136d8dfd91c26a"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/system_navigator.dart", "hash": "0db5f597f1cc6570937e6c88511af3a9"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_texture.h", "hash": "d1894650bbfe9ba65c6f0b195645a9a5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/checkbox.dart", "hash": "********************************"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/debug.dart", "hash": "17fec0de01669e6234ccb93fc1d171f2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/string_utils.dart", "hash": "603b7b0647b2f77517d6e5cf1d073e5a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_closer.dart", "hash": "cbd0196f25d2f055736beb3052a00c19"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/app.dart", "hash": "8f24c8ed1935c6f08997d0b9acb5bf38"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/linear_border.dart", "hash": "0fa4800227413041d2699ed47918c7f7"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/title.dart", "hash": "e556497953d1ee6cd5d7058d92d4e052"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/context_menu_controller.dart", "hash": "c3ccb5b6cd3df44e6587a4f04dd6a4e7"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/display_feature_sub_screen.dart", "hash": "a6d730f196620dffe89ac987b96ef6c3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib/src/screen_util.dart", "hash": "b42f45e5b9cc360364d8edb9cfefe2e3"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/arena.dart", "hash": "5486e2ea9b0b005e5d5295e6c41ad3c2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/base/base_chart/render_base_chart.dart", "hash": "f30e8441b4500b30f1ac727f1988bd35"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/semantics_debugger.dart", "hash": "2c5021ff8faa0330f66b1c501e8d4b22"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/colors.dart", "hash": "0e708f7885d57fccc31cdb5020c2d9c7"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/text_form_field.dart", "hash": "75fa80ab7762b14e35b11b93da96d4a1"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/physics/tolerance.dart", "hash": "43ef2382f5e86c859817da872279301e"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/input_decorator.dart", "hash": "d9dd226ec96aec60f125c0f1f8d00344"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/gradient.dart", "hash": "2bc2f148be8fffe5f3a6a53fe8bc8333"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_platform_interface.dart", "hash": "59bb1cba1648db956dccb835713d77d8"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/switch.dart", "hash": "1603f38e802a78686ee48e3554da22f8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_response.dart", "hash": "a004396fa64ff2163b438ad88d1003f4"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/page_storage.dart", "hash": "e5a3ca065f292c0f0b0cca0a55df41aa"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/nested_scroll_view.dart", "hash": "6e320dd3d12f0e125541bc4b983dcfa7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/internals.dart", "hash": "6683b2c06b0ec964286b1a54f7e2803f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/xdg_directories.dart", "hash": "737107f1a98a5ff745dd4e3236c5bb7b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/widget_state.dart", "hash": "3c24303086312d7181ffa10d0521029a"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/_html_element_view_io.dart", "hash": "61d3c1705094ee0ea6c465e47b457198"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/radar_chart/radar_chart.dart", "hash": "81ee64348f21f74c9b8d127c5cf4f838"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_standard_method_codec.h", "hash": "dd8da806243743ff943e6de062ddaa3d"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/text_theme.dart", "hash": "796af05466fbe319d5fc699b982ded0c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/path_provider_android.dart", "hash": "eb368258f0f9fe56110bdc238488af97"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_sink_transformer/stream_transformer_wrapper.dart", "hash": "04d38c19b0c3dba61b730122d76ec4d4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb3.dart", "hash": "257ca4608e7d75f1db8d4c3ab710ac70"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/lib/data/repositories/time_tracker_repository.dart", "hash": "4412cdb45a71d30d1ce9d435969fc0e2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/colors.dart", "hash": "c517fb54b3d66b22988ad7c8d07c6f53"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/autocomplete.dart", "hash": "27a4ea7d50fcfd776a5d69fce0cd26ad"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/overflow_bar.dart", "hash": "d2694042e337ac1f2d99602c25be195a"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_dart_project.h", "hash": "33df0b32edf5c119fff6fafaa8359145"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/circle_avatar.dart", "hash": "3ad691d7f4e0dfc9bac177f56b288925"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/async.dart", "hash": "13c2765ada00f970312dd9680a866556"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/outlined_button.dart", "hash": "438f80a3d5361329aa6113e3409440aa"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/trace.dart", "hash": "dcb1bf21d8afb364e20a47f106496780"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/sliver_resizing_header.dart", "hash": "9e64d24aeed0ce5534422c6e4b454676"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/icon_theme.dart", "hash": "03d585dfc6055d74a4668e69263afa5a"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/collections.dart", "hash": "f209fe925dbbe18566facbfe882fdcb0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/lib/src/utils.dart", "hash": "95a1512a0fffd6fe7b489b94b4ac91e1"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/circle_border.dart", "hash": "a2aa815908f2e15493e374b9380e558a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/typed_stream_transformer.dart", "hash": "991902b33f1d81c417b707a41341ed59"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system_entity.dart", "hash": "04e7480fb89755fcc5f64f7d80ca610f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/search_view_theme.dart", "hash": "4d673eddc0bd2289539b66a92faae868"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/paragraph.dart", "hash": "a108c1a02c56f9162ede59a7c30ed41d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/boundary_characters.dart", "hash": "9d1525a634d27c83e1637a512a198b4f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/animated_icons/data/search_ellipsis.g.dart", "hash": "c761b80666ae3a0a349cef1131f4413d"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_standard_message_codec.h", "hash": "c9ddf466fd4c06d0991614cd26dd74c5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/serialization.dart", "hash": "f20071b459b9bbb98083efedeaf02777"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/icon_theme_data.dart", "hash": "eca4f0ff81b2d3a801b6c61d80bc211c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/run_guarded.dart", "hash": "ddefd207562d7e33dc44d433e0848e1d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/win32_wrappers.dart", "hash": "af7270fd3861278053b1c45a7b66ece3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_buffers.dart", "hash": "4b495ff6681b3a7dda3f098bf9ecc77d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/scatter_chart/scatter_chart_painter.dart", "hash": "df88f06d397d696d75aec00ead3c3f3b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/bar_chart/bar_chart_helper.dart", "hash": "a487e54bb1cc59d6b0a3a61602745ffd"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/icon_button.dart", "hash": "5ac341d21fd38e1a3307100a5b3c3138"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/borders.dart", "hash": "5de15d7a41897996ef485c087ef4245b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/temperature/temperature_cache.dart", "hash": "a6350a577e531a76d89b24942fca3073"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/orientation_builder.dart", "hash": "177fda15fc10ed4219e7a5573576cd96"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/image_stream.dart", "hash": "9bf11cc1ea784a251bf67350f02f910f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/change_notifier.dart", "hash": "98f06a29791e4f6ffc1ccefd18f323fb"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/navigation_rail.dart", "hash": "12a21ff35182c138908274c8b66714d9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector4.dart", "hash": "299bd3979d7999412945ac4e3199cdcf"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_standard_message_codec.h", "hash": "c9ddf466fd4c06d0991614cd26dd74c5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/text_selection_toolbar_layout_delegate.dart", "hash": "82604e7dbb83dc8f66f5ec9d0962378b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/image_icon.dart", "hash": "2610f7ca2c31b37ad050671aafbccdd9"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/animation/curves.dart", "hash": "74a89d22aa9211b486963d7cae895aab"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier.dart", "hash": "e07baf43a89b4a1225ab8dab1161d2be"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/clip.dart", "hash": "26312d25d45c45d94edcfbaaec9217b4"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_texture_registrar.h", "hash": "455c45888f33294a5054d8adacac5a2e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/src/multi_channel.dart", "hash": "51eb44211d0dcb7fd159fd3232f15135"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/button_bar_theme.dart", "hash": "0f717ff4ecfdaa0347894abbedd5d1e9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding.dart", "hash": "328ff975234df68963cb19db907493ff"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/slider_theme.dart", "hash": "b0aac7d00e469646d25550d1e4e77d12"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/bottom_tab_bar.dart", "hash": "019f7b771f1865632d5a36c9e74296db"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_scheme.dart", "hash": "7536ace8732469863c97185648bb15a9"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_string_codec.h", "hash": "5edcd4ae8b7d3acb2e56bf8bfad75d4d"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/filter_chip.dart", "hash": "0e13760edcb9f90f659ba77c144a3461"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/flex.dart", "hash": "92137effa05660558f35cfc5845783bc"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_basic_message_channel.h", "hash": "6cba49677d30f16cadf943f2648123fc"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/converter.dart", "hash": "ed5548873fcf5a0a5614fc52139600b8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location.dart", "hash": "fb2c02d4f540edce4651227e18a35d19"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib/src/screenutil_init.dart", "hash": "81645f9b5649a11f5ddd2da06a273a2b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/app_lifecycle_listener.dart", "hash": "8e043971337ae96a1e56aaf2256540ae"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/top_level.dart", "hash": "15439eaa12b927b0e9a42b9d168e3371"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512.dart", "hash": "e4973bdb8ceac8b88cdefee5f56f0fa0"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/animation/tween_sequence.dart", "hash": "eabd3dc33b1a3a2966fa68f6efeb6bce"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/enums.dart", "hash": "f4b67c136a2189470329fd33ebe57cb3"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/elevated_button_theme.dart", "hash": "484329e20b76c279413a7d6dc78b3223"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/popup_menu_theme.dart", "hash": "384c15d93757a08ae124e6c2edeb4e9e"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/unicode.dart", "hash": "8b525140e1bf7268e1681a62c7640eea"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/dialog_theme.dart", "hash": "8383986e94be1a258a59af29b9217876"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/reorderable_list.dart", "hash": "1d893e6d648c41d8e3281a76a2320431"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_pixel_buffer_texture.h", "hash": "fb73b65a52e89c14449cd8524058187b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/widgets.dart", "hash": "9f9b1fcdf4037b3b4c71ed65b57e87f8"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_plugin_registry.h", "hash": "9093dc992b7f0c62c899bb5ee75437bb"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/text_selection_toolbar_text_button.dart", "hash": "91bf94aea1db708a8378fa41de066d33"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/single_subscription_transformer.dart", "hash": "789cc727406d0343a1dddb5018570adf"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/toggle_buttons_theme.dart", "hash": "262d1d2b1931deb30855b704092d3cb4"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/material.dart", "hash": "f485bc1aa4fbdf87e17bfb8f80e39258"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_map.dart", "hash": "b6bcae6974bafba60ad95f20c12c72b9"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/scroll_controller.dart", "hash": "ec48414c6983150c30241ba7128634fa"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/implicit_animations.dart", "hash": "c9105f08cb965dfc79cdbe39f062d6c2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/delegate/stream.dart", "hash": "809f1f0bbe7ee77e69f003952a5525d5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/unmodifiable_wrappers.dart", "hash": "ea7c9cbd710872ba6d1b93050936bea7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf8.dart", "hash": "329d62f7bbbfaf993dea464039ae886c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/types.dart", "hash": "13e6a7389032c839146b93656e2dd7a3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/encoder.dart", "hash": "dbf4f1e95289bc83e42f6b35d9f19ebe"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/cam16.dart", "hash": "ca959e5242b0f3616ee4b630b9866a51"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/dialog.dart", "hash": "b3f8f8ba0560319908ddb5d9480a5788"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/flutter_linux.h", "hash": "1dcce1af8eb3052e29d06a756f0535db"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/sink_base.dart", "hash": "8fec1bb0c768b230066dba96aac40ff5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/selectable_region.dart", "hash": "ced9d2439e23015bfc2bac438f598985"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/messages.g.dart", "hash": "3f45d05cfb9a45bf524af2fa9e8fb6e6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/canonicalized_map.dart", "hash": "f5e7b04452b0066dff82aec6597afdc5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/radio.dart", "hash": "9802442b82d3be84abecae8d0a2c7bd6"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/context_menu_action.dart", "hash": "84f94e87e444ce4ebc562b2707348a8f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/binding.dart", "hash": "f6345e2a49c93090bc2e068a0a808977"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/form_row.dart", "hash": "5f64d37da991459694bce5c39f474e5f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/colors.dart", "hash": "f59aed120736d81640750c612c8cfe5c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/preferred_size.dart", "hash": "dd518cb667f5a97b3456d53571512bba"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/scroll_metrics.dart", "hash": "6f18c18a1a5649f27b6e0c29dfba4dc9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/extensions/text_align_extension.dart", "hash": "59f0d9fa64905482ce8f6532d57426aa"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/extensions/side_titles_extension.dart", "hash": "c024f0b097ca90ea66fbb8097be98b26"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/memory_allocations.dart", "hash": "c7c757e0bcbf3ae68b5c4a97007ec0b9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/charcodes.dart", "hash": "a1e4de51bdb32e327bf559008433ab46"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/platform.dart", "hash": "cbf041463d4a85115a79934eafe8e461"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/icudtl.dat", "hash": "692337664e861ad322138061132dddc6"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/context_menu.dart", "hash": "02f1d44813d6293a43e14af1986519ff"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_method_channel.h", "hash": "5a5b80bcf4d72767c5fee74693a1140e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/http_parser.dart", "hash": "b76ebf453c4f7a78139f5c52af57fda3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/src/guarantee_channel.dart", "hash": "68928ed10e0a0df76e1e28584b7256c1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/utils/canvas_wrapper.dart", "hash": "f5b2b0cf4ef806b370b4b21d155c998e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/src/equatable_mixin.dart", "hash": "0f5d8dd74761633229f5cf2fd6358e05"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/_isolates_io.dart", "hash": "f90beedee11a434d706e3152bfb2fd15"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/bar_chart/bar_chart_painter.dart", "hash": "388c5187e56f2c938fa37f35c0a2262b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/system_sound.dart", "hash": "39f5f34a4d3615c180c9de1bf4e8dde8"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/pointer_router.dart", "hash": "8c1a2c1feaeb22027ba291f1d38c4890"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/ticker_provider.dart", "hash": "0119e0f7758ee8ef19baeae2b96cb389"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/flow.dart", "hash": "34ebb85f7f2122d2e1265626cf252781"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_streamed_response.dart", "hash": "f179ed2f20226c436293849c724b2c4d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/radar_chart/radar_extension.dart", "hash": "768067e738f8af0c773a71c3e454910f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/file.dart", "hash": "dcef90946d14527736cde04a54d334db"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/inherited_model.dart", "hash": "940daf4491e3ab2e15d7eac5d6ce6b23"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/navigation_bar_theme.dart", "hash": "b5eb2fd4d6d9a2ec6a861fcebc0793d2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/comparators.dart", "hash": "8ac28b43cbabd2954dafb72dc9a58f01"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/extensions/bar_chart_data_extension.dart", "hash": "81c45842aae33b39d2fa3f467408ab49"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/safe_area.dart", "hash": "7088cc45b21c93be6b42dc748fc3a29a"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/list_body.dart", "hash": "18223495a47aa96889552c9834042729"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/utilities.dart", "hash": "3f5e8feebce49c954d9c5ac1cda935c1"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/toggleable.dart", "hash": "33ce088a133276cbfd4a33ec49bdcb62"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/paint_utilities.dart", "hash": "853b1406f2756bef671f6d57135606f9"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/_bitfield_io.dart", "hash": "0ae47d8943764c9c7d362c57d6227526"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/auto_dispose.dart", "hash": "ef220252cc1911073575cfbf66f4c8d1"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/image_decoder.dart", "hash": "893548eaf87a8fd903da6fa761ad5ec1"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/lib/app/theme/app_theme.dart", "hash": "e72f0f65ef2691ecb5b3b42fdd13d0c2"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/animated_icons/animated_icons_data.dart", "hash": "ac08cb84358e3b08fc1edebf575d7f19"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/text_selection.dart", "hash": "9c13d1f810b039faf38c54f062c83747"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/focus_scope.dart", "hash": "fddd73db94bb2fa3a0974bed845f32a8"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/drawer.dart", "hash": "f26e2cb53d8dd9caaaabeda19e5a2de3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_async.dart", "hash": "255fd9cb9db57da2261cb7553da325ab"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/inherited_theme.dart", "hash": "7ebcf3ce26dea573af17627d822e9759"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/scrollbar_theme.dart", "hash": "b3019bcd49ebc4edd28b985af11a4292"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/tooltip.dart", "hash": "b9bfa2dc31960df2f1fd3aee88c3807e"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/lib/presentation/widgets/statistics_chart.dart", "hash": "6fc5dc4bd32464a28e2e78bd2fdd6ccf"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/restoration.dart", "hash": "b3465d5b02dd4743d8d9f9e4170a1151"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/consumer.dart", "hash": "f28a95b717859fa14ea8344e766e7fb0"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/adaptive_text_selection_toolbar.dart", "hash": "9ec81b597c30280806033b70e953b14c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/future_group.dart", "hash": "fb71dd46672c822515f03f8f0dddbcb8"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/draggable_scrollable_sheet.dart", "hash": "9b76b249fb23172215a62d66bb393ed5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/base/axis_chart/side_titles/side_titles_widget.dart", "hash": "5698879661f85d0b4d6b2a889dda8c5b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/search_field.dart", "hash": "7b71540e417e6ea3f1134b4b677e0624"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/arc.dart", "hash": "511ff5c6f0e454b22943906697db172f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/stack_frame.dart", "hash": "eb89408ce23b2abcd324ea5afb05a1ea"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider.dart", "hash": "7504c44d1fa6150901dd65ec78877be0"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/shared_preferences_async_foundation.dart", "hash": "282aeeb78f4a92064354b5fe98161484"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/text_selection_toolbar.dart", "hash": "8dedd49e916a59b6940a666481d82e10"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/path_provider_platform_interface.dart", "hash": "09b3f3b1ef14ce885c016f2eba98f3da"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/scale.dart", "hash": "2c777edec67bbb084e5608fb5f6b495b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar.dart", "hash": "b1884cfd8778cd71cea03ca8f4b39f4f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/strut_style.dart", "hash": "ee62fb3be5d885d65054fac4b84cac6c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/context_menu_button_item.dart", "hash": "083722b0880e8e5981f9e33da11e449c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_provider/base.dart", "hash": "63b92eb56c14d5474db11677f1800c83"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/ink_well.dart", "hash": "203fbbac922589879ae44083b04a368b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/refresh.dart", "hash": "7d5bd66d61c58afe63c6d33ee0e421c1"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/rounded_rectangle_border.dart", "hash": "ec0bf24485bc5f9b825a382457f586e2"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/platform_view.dart", "hash": "a8513860b3b4c160b57ca6264bc0acf8"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/stadium_border.dart", "hash": "85814d14dae3bc1d159edd0a4bef48e4"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/time_picker_theme.dart", "hash": "b269f9d6378b540b7d581db466ad98d3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_client.dart", "hash": "32a40215ba4c55ed5bb5e9795e404937"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/constants.dart", "hash": "0672d853d5097a03eddc7dbe558eeabd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/auto_dispose.dart", "hash": "a57c7d0bb0b0f3ff52fd48c953453bd4"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/placeholder_span.dart", "hash": "d2386b256656121d501a16234b008e2b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_provider.dart", "hash": "edc6185b4e4994b45acda6675696d87b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/scheduler.dart", "hash": "95d8d1f6a859205f5203384e2d38173a"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/app_bar_theme.dart", "hash": "eafb3b31ec7cebf556a529810d6f649a"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/image.dart", "hash": "4eede9144b4c0e4b14bd426654183174"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_mixin.dart", "hash": "89dc3f84db2cd1ea37e349fdb1de09bb"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/autofill.dart", "hash": "577ec098e9f6651d7704fad48b4dd44a"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/.dart_tool/package_config_subset", "hash": "95eaf6067e4f0e40008f3314ccadd5c7"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/selection_area.dart", "hash": "ed28f6ca17f72062078193cc8053f1bb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/plugin_platform_interface.dart", "hash": "8e49d86f5f9c801960f1d579ca210eab"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/font_loader.dart", "hash": "a29f0df228136549b7364fcae4093031"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/selection.dart", "hash": "cc4a516908b08edff4fade47d6945e5c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/box_decoration.dart", "hash": "********************************"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_view.h", "hash": "e27fc7ec7888b59ffac04a45e4461699"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/expand_icon.dart", "hash": "3f7c50b425818ea563c8459cfd6f9d5a"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/lib/presentation/providers/app_providers.dart", "hash": "a1b8c2a6e1b561564d4b4032e2989d86"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_method_call.h", "hash": "96e3a074c3f1f33e88ad3ccc60d6f9d3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/src/equatable_config.dart", "hash": "e0f2b097829216421823bda9ec381cab"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system.dart", "hash": "06c73ad137e5db31d7e6ba4258ac13c7"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/predictive_back_page_transitions_builder.dart", "hash": "cb745b78bdb964c02c1c4a843b9c1e7d"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/size_changed_layout_notifier.dart", "hash": "8a39bdc324d0ff25097784bd98333c08"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/stack_trace.dart", "hash": "9a478fed4f2f15993c892e33f6fd766b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/path.dart", "hash": "********************************"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/debug.dart", "hash": "dbb0bb20c79bcea9397c34e3620c56c3"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/autocomplete.dart", "hash": "72c318c3499a7a4d533965d32c6dface"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta_meta.dart", "hash": "0cf5ebf6593fabf6bb7dfb9d82db735b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/decoration_image.dart", "hash": "6b48e1348ae677efad30c0a9d4600e38"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/choice_chip.dart", "hash": "3cd5a71cfa881a4d3d6325d6b2c6d902"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb2.dart", "hash": "f8fb1733ad7ae37b3d994f6f94750146"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/lib/src/io_web_socket.dart", "hash": "5b77b184a7c7fe214ed7b0e8dc221381"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_zip.dart", "hash": "df699735e3bcd730f16ce377d562f787"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/scribe.dart", "hash": "d195153a8c01a0392b38e3b9adc672d8"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/widget_span.dart", "hash": "84e117adf104c68b0d8d94031212b328"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/dismissible.dart", "hash": "c98d71a32518e80bc7cf24b1da6c9c57"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/animation/animations.dart", "hash": "ebef4cfdfb854b138f6bdbbf53e73f0f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/stack_zone_specification.dart", "hash": "d0268b4d80612385359eadd2d6ddb257"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/lib/src/channel.dart", "hash": "926894fd05495a4558aaf9e8f1da1451"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/method_channel_shared_preferences.dart", "hash": "513d6195384503beeb7f3750e426f7bb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/charcode.dart", "hash": "b2015570257a2a6579f231937e7dea0e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/vm_trace.dart", "hash": "9a7022bcfa03c67d126e948062508201"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/ray.dart", "hash": "********************************"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/raw_keyboard_listener.dart", "hash": "********************************"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/drawer_header.dart", "hash": "f996ce49eab57718350b84e11ea3192d"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/_web_image_io.dart", "hash": "e88b0574946e5926fde7dd4de1ef3b0d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_provider/auto_dispose.dart", "hash": "34f75dd4788c48a2a7b2ce2efe4c51fe"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_provider/base.dart", "hash": "8e16702463aaa9f1da9da189aabae66c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/colors.dart", "hash": "65c7fba34475056b1ca7d0ab2c855971"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_notifier_provider/auto_dispose.dart", "hash": "d2e52f81da2329303a3f9d4b369c3320"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/modal_barrier.dart", "hash": "830b9f37313c1b493247c6e7f5f79481"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/base/axis_chart/axis_chart_scaffold_widget.dart", "hash": "c8b0dc272efb68fa2639a63f2e92aeba"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/keyboard_key.g.dart", "hash": "4f9995e04ebf5827d1352afca6adda26"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/checkbox_list_tile.dart", "hash": "********************************"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/divider_theme.dart", "hash": "04f538d5fc784c89c867253889767be4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/selector.dart", "hash": "c771f26d18f9897af0e13e3a2c83d5ed"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/default_selection_style.dart", "hash": "bbc9542eb5e3c4701c24bc1268b8165c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/frustum.dart", "hash": "d975e51852aa1802c81c738dcb4c348d"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/animated_switcher.dart", "hash": "008b3ea4691331636bbea9e057357ceb"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/two_dimensional_scroll_view.dart", "hash": "e138cb83b907c09a4ac468dff69d43de"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/equatable.dart", "hash": "1a5f064d497f9539e8e2cb4ba15a8f05"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/text_style.dart", "hash": "0cf873bc441372ec89d746477273af13"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/client.dart", "hash": "b16458199371a46aeb93979e747962a3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/unicode_glyph_set.dart", "hash": "cdb411d670a094822c46ead81fc1c4f7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_request.dart", "hash": "8ac37c0f7bea9c97df2a0bef6bb3f858"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_zip.dart", "hash": "1dac993c7444b99a17f2dcf45acaca97"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/http_date.dart", "hash": "fb76e9ed5173ac1ae6a6f43288581808"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/banner.dart", "hash": "674ba42fbba2c018f6a1a5efd50ab83e"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/material.dart", "hash": "ff1b06a4c51e36902ef2e5cf96495fea"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_completer.dart", "hash": "b9531c458d313a022930a0842db8201e"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/force_press.dart", "hash": "d3de616e525e730c7b7e3beb57930993"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/utils/lerp.dart", "hash": "10413a05296db73b1d2d00ab94054ba8"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/fractional_offset.dart", "hash": "e7b2de136a99cf5253477d4fb4138394"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/utils.dart", "hash": "fe2489ea57393e2508d17e99b05f9c99"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/result/capture_sink.dart", "hash": "7c57a9163e2c905ac90a6616e117766f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/bottom_sheet_theme.dart", "hash": "be66f00d2c9bb816f4236dd0f92bff55"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/team.dart", "hash": "f6c6b31745eec54a45d25ffe6e5d7816"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter_tools/lib/src/build_system/targets/common.dart", "hash": "7b70bab5ac21ac24af3c971965617277"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/button_style.dart", "hash": "982099e580d09c961e693c63803f768d"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/animated_scroll_view.dart", "hash": "62f6d0411965eefd191db935e6594f90"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/scroll_position.dart", "hash": "94c0c017ccb267b7cacc7c047ee5b9c3"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/monodrag.dart", "hash": "8807672a31b470f53c5fcc2b36dcf509"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/paginated_data_table.dart", "hash": "0434e70443094435172ff3d214d26bba"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/_timeline_io.dart", "hash": "90f70ffdd26c85d735fbedd47d5ad80b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/image_resolution.dart", "hash": "0f2a1a61119c0bef3eaf52c47a2ebcf4"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/disposable_build_context.dart", "hash": "1fc85ca774e46295ca83c157718278e4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/extensions.dart", "hash": "38e17b28106d00f831c56d4e78ca7421"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/magnifier.dart", "hash": "4da5ad5941f2d5b6b3fbb3f7ea217b41"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf16.dart", "hash": "10969c23d56bc924ded3adedeb13ecff"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_set.dart", "hash": "1b20a6e406ca8e79675b2ebd9b362d10"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/edge_insets.dart", "hash": "00dfe436d7f3546993ad86cc4f9ff655"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix2.dart", "hash": "7f164e577cfcf8c8295947195cde2a7c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_link.dart", "hash": "600a83d8e8dcbc1fde99887eea16f18e"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_binary_codec.h", "hash": "5997440e26e079541337c498925226f1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_async_platform_interface.dart", "hash": "03664e80d73ff10d5787d9a828c87313"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/scrollable.dart", "hash": "c8260e372a7e6f788963210c83c55256"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/activity_indicator.dart", "hash": "0e3d746a279b7f41114247b80c34e841"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/button_style_button.dart", "hash": "6a7d9ee6c8fae5e9548911da897c6925"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/lib/presentation/providers/theme_provider.dart", "hash": "b65b34ea97f2cd90daad7eb5bf3e1f5c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/menu_theme.dart", "hash": "89ae530b1eb1ce798ec54bc9b45efdba"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/async_memoizer.dart", "hash": "abcb2d6facc18b2af070cb86cbb1c764"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/lib/web_socket_channel.dart", "hash": "77227643b5a655ab1fd24917c5a4675c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/sliver_prototype_extent_list.dart", "hash": "9645e1d88d63387bb98a35849f4cbe53"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/floating_action_button_theme.dart", "hash": "08c3fd9ed1607d3a707ffe9b3532218a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/fl_chart.dart", "hash": "99f712e70679ca80a6f69cd6c8c86bb1"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/pinned_header_sliver.dart", "hash": "4e04af41f89adf9231bad1579f5bb9a1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/allocation.dart", "hash": "9d62f4f58e8d63a8e106a1158eb13a02"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/semantics/semantics_event.dart", "hash": "f90fd4f8a9988f08157d132c23c8c08d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location_mixin.dart", "hash": "6326660aedecbaed7a342070ba74de13"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_notifier_provider/base.dart", "hash": "0ab8c6ae2a539e1eee8cc8e4e7cac2d1"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/mouse_tracking.dart", "hash": "5da121a0d3087e7cf021bfcdeb247b77"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system.dart", "hash": "3120b9b427a566f796573ee37167c026"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/animated_icons/data/menu_home.g.dart", "hash": "edbd68eb36df4f06299204439c771edd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_set.dart", "hash": "4b5d82ddeb09bc46ae0e980616ce0109"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/lib/core/constants/ui_constants.dart", "hash": "89d77ee7376ea902b9f28cb3f58bb6ee"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/lib/data/models/app_model.dart", "hash": "39267c948328600f6aed858a644c07f7"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/range_slider.dart", "hash": "cf5dc26d65244c12416f3411c6d79996"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib/src/r_padding.dart", "hash": "72c755438ac184c534cebeb8b335ed97"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/base/base_chart/fl_touch_event.dart", "hash": "c8ba4ee305acb51fd51c8090fe306816"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/primary_scroll_controller.dart", "hash": "58707cf455f97f907192b4ff92d36711"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/path_provider_windows.dart", "hash": "38dc31b8820f5fd36eedbf7d9c1bf8d9"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/lib/presentation/widgets/app_list_widget.dart", "hash": "c84e15d3beaeacb9310d1e84997908a4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/base/axis_chart/axis_chart_helper.dart", "hash": "ca983c369ebd19fbeb07632d218d8a8f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/toggle_buttons.dart", "hash": "64a2ea17e8058aec30096102af030f98"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/text_layout_metrics.dart", "hash": "13be7153ef162d162d922f19eb99f341"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/navigation_toolbar.dart", "hash": "5be90cbe4bbf72b0264413e4ccb5c275"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/guid.dart", "hash": "55bb53dd4f9ed89c9ff88c204b59293c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/exception.dart", "hash": "5275d424aba5c931a30e6bd3e467027d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/method_channel_path_provider.dart", "hash": "77ed8d7112753d0eeaa860ecd9fc5ba0"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/elevation_overlay.dart", "hash": "ea5bbc17f187d311ef6dcfa764927c9d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/provider/auto_dispose.dart", "hash": "a3250d5fb60cc8b17997c886a67be737"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/key.dart", "hash": "3ee6304161ca2993b303a8074557fe66"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/messages.g.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/base/axis_chart/axis_chart_widgets.dart", "hash": "9de31337dc9c94f3000cbdd28d8e39fe"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/scheduler.dart", "hash": "1ac1f41185397129f7ea925130f188f2"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/page_view.dart", "hash": "4372cb3b63b820aff3fe67061bba3f9f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/undo_history.dart", "hash": "73089c9737db54a05691e09bc9fc1bcd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/auto_dispose_family.dart", "hash": "c8b1bc30159a132cab814de0d71e0462"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/keyboard_inserted_content.dart", "hash": "5da306e7f2542e5fb61efff6b4824912"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_extensions.dart", "hash": "5843b4750179f6099d443212b76f04a2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/obb3.dart", "hash": "5ca0b5786bf63efd4fc72fcecfe1b36c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/divider.dart", "hash": "428549777327ddf7f2287b69cab7b68b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/text_theme.dart", "hash": "ee36aadc3fac54d5659c94c6aadcd007"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/services.dart", "hash": "bab8606629135509c96d78f7253526ed"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/radio_theme.dart", "hash": "3f2a39352a1c6067566f8119aa021772"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/result/future.dart", "hash": "18c04a8f8132af2c1b1de5af6909025c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/input_date_picker_form_field.dart", "hash": "e3b1d07a31d08470207f2b668564a833"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/placeholder.dart", "hash": "a69e90f683dddaf61ae8d7f094219026"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/constants.dart", "hash": "be94b8f65e9d89867287dabe5ea1dff1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_with_context.dart", "hash": "a8f2c6aa382890a1bb34572bd2d264aa"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/print.dart", "hash": "458f3bf784829a083098291a97123e81"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/messages.g.dart", "hash": "1567572a579e5f2aab31966d4a056855"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/chain.dart", "hash": "1112185143b6fe11ce84e1f3653b2b6b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_map.dart", "hash": "9d273d5a3c1851b0313cd949e7f84355"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/semantics/binding.dart", "hash": "7f662c8207cea5db3d45f239a277ca9c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_sink_extensions.dart", "hash": "3a2d505268f5446e5f7694776b69b407"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/scheduler/priority.dart", "hash": "ac172606bd706d958c4fe83218c60125"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/error.dart", "hash": "6cae6900e82c94905cc2aaefd806f8eb"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/beveled_rectangle_border.dart", "hash": "d8060c05b658b8065bc0bfdff6e4f229"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/popup_menu.dart", "hash": "e6f282a4b33b70c7d1d06bec39b155f8"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/lib/data/services/mock_backend_service.dart", "hash": "c09b503d28ef946de7282182feee27fa"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/banner_theme.dart", "hash": "355538055d623505dfb5b9bae9481084"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/bitfield.dart", "hash": "d33374c0857b9ee8927c22a5d269de9b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/result/capture_transformer.dart", "hash": "e82a9b67ba33ae635b9b083ef147fb9b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/table_border.dart", "hash": "bbc7eccdbd8472a2180e0dffce323bb9"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/licenses.dart", "hash": "c0cf85f80b79542d2b0e1a00547d7310"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/button.dart", "hash": "782760e5709624f38ebac3b7c728a792"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework.dart", "hash": "d856ca958740bf8a240738ad9e9e69c2"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/sliver_floating_header.dart", "hash": "5ffb77551727a0b5c646196e7bf1e9bc"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_json_method_codec.h", "hash": "c31254629784a8fd48a5ec671df85e64"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/service_extensions.dart", "hash": "7abc7e5212374d29bfe5372de563f53c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/icon_theme_data.dart", "hash": "ae1f6fe977a287d316ee841eadf00c2b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/constants.dart", "hash": "83df4f6e4084a06a4f98c27a524cc505"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/animated_icons/data/play_pause.g.dart", "hash": "9ad11b4bdb179abe4ccb587eb0e2aebc"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/wrappers.dart", "hash": "21e56afda1f096f0425a34987708ed56"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/value_listenable_builder.dart", "hash": "68c724edcc385ae2764308632abb76b4"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_value.h", "hash": "0fad916a7405474cce0f6d85509a12ac"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/interactive_viewer.dart", "hash": "bb7bcb463df2ae0f5f952d439fdb384e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha1.dart", "hash": "dfb8ebcfda08e6d9b294f49d74ad9f98"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/live_text.dart", "hash": "7da554c3a69a1c2d019202e3f63331c5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/eager.dart", "hash": "07664903d8026f2514b29b786a27f318"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/animated_icons/data/ellipsis_search.g.dart", "hash": "7018ea64a9aab18f27a10711285d7573"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_method_response.h", "hash": "63f9825adadb5fe17a5f3df6a8202686"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_engine.h", "hash": "1b028c1fa72fa880b3f8ceb15c1c69df"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/path_provider_linux.dart", "hash": "8ac537f4af05ad812e8cd29f077aee24"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/segmented_button.dart", "hash": "ad631d7cd122efc4862c1c084fbde716"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/scheduler/binding.dart", "hash": "505d7dde41bffe17b69e52db6ab37d0c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/result/error.dart", "hash": "056ba78280a44883e05c65a88771b4e8"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/radio.dart", "hash": "9b1cee1f8aa8b638cad928232383b02b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/user_accounts_drawer_header.dart", "hash": "bda2eeb24233fd6f95dc5061b8bf3dd5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fruit_salad.dart", "hash": "3c8d2d2b73f69d670141d376642e5252"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/overscroll_indicator.dart", "hash": "247fd4320e1e277acc190092bf6d35ae"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/scatter_chart/scatter_chart_data.dart", "hash": "af865acf3ab953c13d4a814331481873"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix4.dart", "hash": "b5f0b0da99e8a07d58c21ae071800404"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/ffi.dart", "hash": "ae66b0cbdfe2e2a5a99c5dfa48fd5399"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/inline_span.dart", "hash": "8199cdd8c075bef2ed0811394702680d"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/.dart_tool/flutter_build/494b83a84f91cd1524865c5c4d87b1ae/app.dill", "hash": "77653fc66cb082b4d0cfc4a78b4381ac"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib/src/equatable.dart", "hash": "52138432903419f8457bcad45e5e6e99"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/mouse_tracker.dart", "hash": "0c402ad9ba3f3e4d7f45f24b27447ec2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/extensions/fl_titles_data_extension.dart", "hash": "86a73832d96fbf3b74722bd304718fc5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/async.dart", "hash": "3f9362642d37e0d97860181e8a1dd598"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_extensions.dart", "hash": "903d8536aa6c9e6926e96e9a2b449824"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/material_dynamic_colors.dart", "hash": "81bf43e01741bf8b9df15ec37ffbc9ea"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/scatter_chart/scatter_chart.dart", "hash": "40dc2e4370dfe6ef48fe74578efb104d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id_real.dart", "hash": "0e5b422d23b62b43ea48da9f0ad7fd47"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib/flutter_screenutil.dart", "hash": "15f1990c0ac3146200fcbdcff0779a7c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/animated_icons/animated_icons.dart", "hash": "97f7922aea45c38413930285b604bf18"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/span_scanner.dart", "hash": "87bcefcfff19652ad296ec7005799840"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/text_field.dart", "hash": "53cf0d76bfd70bfdc7e2edb4a18327f4"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/standard_component_type.dart", "hash": "09973ba0a94d2d819052c0544dcdce70"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/path_provider_foundation.dart", "hash": "9485ecc20aafb0727c2700cf6e34cb65"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_neutral.dart", "hash": "3ee18da390e16ca65f2ef168adb8a1ef"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/radar_chart/radar_chart_renderer.dart", "hash": "273349a57d6dec42cb2151a0225576d9"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/slider.dart", "hash": "737365e0b93f911e49f1ac1e5363564c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/actions.dart", "hash": "1c7764fa08241a44711301c74fb658df"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/crypto.dart", "hash": "3b0b3a91aa8c0be99a4bb314280a8f9b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dislike/dislike_analyzer.dart", "hash": "d7eb1678ec74acd9857a4193fd62ed5b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/sliver_list.dart", "hash": "03001d3ddae80bbf1f35c5e70e0d93e4"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/object.dart", "hash": "74902317f9caa3ba9c05b114d45d8a02"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/overlay.dart", "hash": "2de077d432c4bb0a9525e9ab5d84913a"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/app.dart", "hash": "db24bbb74875ecb216e8445bc10a0714"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/mergeable_material.dart", "hash": "db6f70d83d36597cc6bc3eaaffd10aaa"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/process_text.dart", "hash": "94235ba74c3f3ad26e22c4b40538ce07"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/tween_animation_builder.dart", "hash": "107c33a245427bf0f05e21c250653dc6"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/tap.dart", "hash": "0a546a51fffe9612c8c3cbebc609691c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/foundation.dart", "hash": "b4a0affbd6f723dd36a2cc709535c192"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/scrollable_helpers.dart", "hash": "7f2ccd6eece375fce2e247d3995e45c5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/browser_context_menu.dart", "hash": "db4a14227247e2524e46f6b0dd9da267"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/matrix_utils.dart", "hash": "59475498db21e2333db54d6478af7c94"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_sink_transformer/handler_transformer.dart", "hash": "81a6a107cbfd5dc1c55af9a93189bc5d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_sink_completer.dart", "hash": "2430a12d4750c3c76ef07d29bb6f6691"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/delegate/future.dart", "hash": "443fe4357544b85c13ef051cf37a602f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/auto_dispose.dart", "hash": "7c89e8d3e17b2ff04570b741ce311e44"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/lazy_stream.dart", "hash": "1649ee82914f6ad1fd46de466dc03378"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest.dart", "hash": "d623b1e2af43bcd9cde14c8c8b966a8b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/icons.dart", "hash": "32b222420709e8e40d12f6ea9fc0041e"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/synchronous_future.dart", "hash": "fb23ec509c4792802accd10fa7c8a6b0"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/text_form_field_row.dart", "hash": "f30e48d0892af0c99b54816673cff9ab"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_notifier_provider.dart", "hash": "332fc1055d849f61ff8cb6ab6a919d1a"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/object.dart", "hash": "daa0c9b859ed1959e6085188a703f387"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/slider.dart", "hash": "1ae1a412c9f9daff34b9dd63e60cec2d"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/raw_keyboard_linux.dart", "hash": "2936a409e1029ec52f7c0003f4db18c4"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/box_border.dart", "hash": "********************************"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/stack.dart", "hash": "2cf5ffb71954128b5e80f17a36bcde43"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/relative_span_scanner.dart", "hash": "b9c13cdd078c3b28c3392f0d6d5d647b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/platform_selectable_region_context_menu.dart", "hash": "aef544fef0ced7679e0edaf5f8d036b7"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/text_editing.dart", "hash": "9298606a388e3adb5f1bbe88ae45b1e6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/null_stream_sink.dart", "hash": "cc0ab0117e8a0a54ec3efe6d9251860e"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/desktop_text_selection_toolbar_button.dart", "hash": "a46ede2164234d7371852e8f57865dd0"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/text_button_theme.dart", "hash": "becd419f96efe14f36f18a8c8adc82cd"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/dart_plugin_registrant.dart", "hash": "44b8efa69ec831d1a0ce74c20ecc27b4"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/service_extensions.dart", "hash": "920b63c794849c8a7a0f03f23314bbb1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/frame.dart", "hash": "49286617067167600a8c7357dff1dcfd"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/sliver_persistent_header.dart", "hash": "2a374faf6587ee0a408c4097b5ed7a6e"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/switch.dart", "hash": "1e840a2c03797a7468018e124b957d2f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/adapter.dart", "hash": "e05529d31a09e4c86cde70483824fa10"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/icon_button_theme.dart", "hash": "ac317f8ed3b04bec644817e6f60a28d7"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/text_button.dart", "hash": "dbbc7f46620d816e615bbbe67eb258e7"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/navigation_rail_theme.dart", "hash": "e472fd233266592e97b3fb39bb1a11dd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_directory.dart", "hash": "18b0559a8cbfb3b3a3d34bbbea4669c7"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/physics/gravity_simulation.dart", "hash": "44c1268c1ecafd3b4cd06ab573f6779a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/line_chart/line_chart_data.dart", "hash": "e60b2ad2a98629d505df00b57688df11"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_rainbow.dart", "hash": "0bc80db5885f9d8ecc0f80ddab6fe8b4"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/button.dart", "hash": "d7a239f8b80f844857527c2012e4fa1c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter_tools/lib/src/build_system/targets/linux.dart", "hash": "ca70051cbdc2ca7f1b5f2086cc18431f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_splitter.dart", "hash": "698b7b5743b9cfa0aa9d08de156d04b6"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/lookup_boundary.dart", "hash": "37f181e3096dc69dc408bf7d07fcd39a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/utils.dart", "hash": "8a7e3b181572ed50e923e5dc05a7533d"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/table.dart", "hash": "9af22b49fd7407bc0ef05667f139defd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/provider/base.dart", "hash": "34d65aad713399e0e95c6d52aea92d88"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/base/custom_interactive_viewer.dart", "hash": "7c2d67ca4f1041eaf1a158310546d430"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quaternion.dart", "hash": "698a6fc4361dd42bae9034c9c2b6cf7b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/view.dart", "hash": "e758d8d6b65597325bd35b5dc769c7a2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/restartable_timer.dart", "hash": "89cdb68e09dda63e2a16d00b994387c2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/messages.g.dart", "hash": "d8a6ceefc2ed13b75c503d01c8911fd6"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/menu_anchor.dart", "hash": "b24af65afbe06cb00d5661df3d3083af"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/status_transitions.dart", "hash": "59b6b74779849bf5b836b84bb362b99b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/value_provider.dart", "hash": "d5a669dc5155cedc975db1022a570128"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/scan.dart", "hash": "acfc0a55deec22276e085dae6197833a"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/text_selection_toolbar_button.dart", "hash": "9a67635cfd2e047d996c4840d4cb18ea"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/about.dart", "hash": "4bf9cb0fbb8b0236f0f9e554c7207a4c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/asset_manifest.dart", "hash": "a2587417bcfd04b614cac5d749f65180"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/box.dart", "hash": "********************************"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/animated_icons/data/close_menu.g.dart", "hash": "a0816d2682f6a93a6bf602f6be7cebe1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/auto_dispose_family.dart", "hash": "3b32647556f88ddd6d625ddc58c7691e"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/drag_boundary.dart", "hash": "1e0ea989110b1544dbaf1fdf3d9864cc"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/layout_builder.dart", "hash": "16903e1f0bc6b66d30a5804b7ae71fe5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/scroll_notification.dart", "hash": "269af8ca7030ccfd9c868fe9af8a6b0a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wu.dart", "hash": "c0da8171c63f0ab4e822dd094fc2c595"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/scroll_view.dart", "hash": "6f3424f2fc515abb888590b75c98e190"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/flexible_space_bar.dart", "hash": "9d6f9dd391f828bccdbb47c5072c04c1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/string_scanner.dart", "hash": "f158ffadca730ab601c60307ba31a5e4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/stream_group.dart", "hash": "d16df8af6c029bc5e12bedcb2d9ed464"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/change_notifier_provider/base.dart", "hash": "b21a009902949ddc4ba80d607867fcb7"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/checkbox_theme.dart", "hash": "********************************"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/material_localizations.dart", "hash": "1b3814e3cd3f2d9543c7ebaf88384e10"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/autofill.dart", "hash": "3623c605586d2e37af23d6b746721bd7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/context.dart", "hash": "daeb052f1089d4e84d8a22acf56c1da2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_legacy.dart", "hash": "4144d8b8e1cae585ab9f01406b3e1f75"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/text_painter.dart", "hash": "1338341fe43eb21f20857cc392cf2f71"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/scrollbar.dart", "hash": "85cf42bafb7c0646bd7a99379649da29"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/sliver_persistent_header.dart", "hash": "ffa4f7b2d5b1caccc05cf4b64021ae5e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterator.dart", "hash": "6c54f90e0db5f42a13be6b3efeb4a04d"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/lib/core/constants/api_constants.dart", "hash": "fd540a4a63ebf59dc01f2e8fa8b6fb95"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math_64.dart", "hash": "bd1315cfa157d271f8a38242c2abd0d9"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_plugin_registrar.h", "hash": "3d850b5432140f7e565baa806ee1f716"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/_network_image_io.dart", "hash": "be7392100d4028793c499a48ed55cf29"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/desktop_text_selection_toolbar_layout_delegate.dart", "hash": "c679063104d2f24639459c8ab3eed77a"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/two_dimensional_viewport.dart", "hash": "7bdfcadf7dd131e95092d30909e5b11f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/future_provider.dart", "hash": "a6705b39e0c01e2fc0e40b8c8c674aac"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/tab_bar_theme.dart", "hash": "a91b4b0d0d10b955e8973126cf288ea4"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_texture_gl.h", "hash": "2ff91c9748bb7c022a2aeee861f51b81"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/base/axis_chart/transformation_config.dart", "hash": "a73d0f240818cef99b369304b28abee7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/pie_chart/pie_chart_data.dart", "hash": "fd0c411e4f5f4ded85a1bc2494696ca9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/core_palette.dart", "hash": "d35b72b249d19f54a4cd6f22ff3299e9"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/platform.dart", "hash": "dd109d67b92b9fbe6e0051f0c890c903"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/painting.dart", "hash": "4bd60bd8ede4b9dad954493d26d3e586"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/banner.dart", "hash": "f979a94d7bd35cf2a5168fbfb9bdcf1f"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/lib/presentation/widgets/app_detail_view.dart", "hash": "732b1992e24db4b1b629e47f2d49ecfd"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/action_icons_theme.dart", "hash": "50dfb9886f462e2b3405f0f8d23f179b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/byte_collector.dart", "hash": "3aaf04a3a450c1b6a144f84f3c778573"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/colors.dart", "hash": "5ed8acdae7dd3501b64b0ff3e33c1f45"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/listen.dart", "hash": "4990e198f887619ece65c59a3de67869"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/button_bar.dart", "hash": "42c4c0281ec179aea5687dbced56aca7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stack_trace.dart", "hash": "bd15738d49bec303fe3d234de40503d8"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/scroll_simulation.dart", "hash": "b29e302994b1b0ea5029734406101b8e"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/floating_action_button.dart", "hash": "55f7619e20765836d6d1c7001cb297fc"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/focus_traversal.dart", "hash": "5af6304445e6664f6caca9ed4b5e885f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/parsed_path.dart", "hash": "cb454929d7810d3ee5aa5fc28283d3fd"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/tooltip_visibility.dart", "hash": "ee2f417f35b5caa4a784b24c1bc32026"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/default_text_editing_shortcuts.dart", "hash": "9a31689295b300aa8ab12d29fb8853ff"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/lib/src/exception.dart", "hash": "a8875f2b3b371e151aab119edb22855a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/tonal_palette.dart", "hash": "44b3c2a3d6e67a3213a49cce58fed932"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/deferred_component.dart", "hash": "53b9028402187f878713225b48bdd5bb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/cancelable_operation.dart", "hash": "57ef1f2eff2168c2e2ba1c3e4e60e05a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/posix.dart", "hash": "5e054086533f32f7181757a17890ae56"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/debug.dart", "hash": "0575a78fbb39a292302737868752da77"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/extensions/fl_border_data_extension.dart", "hash": "4a507f163793d71584798e6223c7577a"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/viewport.dart", "hash": "68eb8647107febe1419211e153b27a54"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/drag.dart", "hash": "43ba7557388f413902313df64e072389"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_application.h", "hash": "5774eb642609ea54649f5a74272b973f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/dual_transition_builder.dart", "hash": "c06267b6c315a5e40f28feb6019de223"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/raw_keyboard_android.dart", "hash": "c9111e47389ee4b70aab720435a2a2df"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/reorderable_list.dart", "hash": "6ea409faabc2d30760053a8936e45796"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/sliver_grid.dart", "hash": "b61a261e42de1512c8a95fd52ef6540d"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/search_anchor.dart", "hash": "490fffadb78eb29c5fe209be7fe12370"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/editable.dart", "hash": "ff7c5f41b6493392c45ef30383f6af9b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/tweens.dart", "hash": "29befe23f841cf5dd2dc7df24c13d88d"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_application.h", "hash": "5774eb642609ea54649f5a74272b973f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/expansion_tile.dart", "hash": "f6816ebd27db772616d01f543b33d0f8"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/fade_in_image.dart", "hash": "b692d4a68a086507a66243761c3d21a6"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/action_chip.dart", "hash": "c7d65c476f653e952aedcb0cbcab3c73"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest_sink.dart", "hash": "038a6fc8c86b9aab7ef668688a077234"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/path_provider_linux.dart", "hash": "b48ba72a2d5d084d297c3d78e351036e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file.dart", "hash": "3e30d0b7847f22c4b3674358052de8b5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set.dart", "hash": "0073f703be7f7ddbd7f04d1b740f35c6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters_impl.dart", "hash": "6297da5be01fb7c0d5c4aaffe7a27a50"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/interface_level.dart", "hash": "1bdb47a9af4b0a5d759937da8ff04db0"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/menu_button_theme.dart", "hash": "e461dc9f79fcf6a9e4faf12c8182fb47"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/desktop_text_selection.dart", "hash": "05d4aeae6031730c6aa412a128f67448"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/list_wheel_viewport.dart", "hash": "2baf11d03f1f50ccef5294c1fe810e25"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/view.dart", "hash": "15957b9d3eac4a2e1acaa24a3032afe7"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/physics/spring_simulation.dart", "hash": "e85b30de1963bb6981d72b6027a66dd4"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/grid_tile.dart", "hash": "9c169d41e4740bbc21d0ce33bc753119"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector.dart", "hash": "6a67d38bafe568f1b4047286d586fbbc"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/text_selection_toolbar_anchors.dart", "hash": "3fa7a3bafbab98c305119475eb004a06"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/routes.dart", "hash": "4591f6273e6282466c0364d5331e50c5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/utils.dart", "hash": "105813825251a3235085757d723ae97c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/physics.dart", "hash": "6e29d5e69c5745a45214fe14da377c1a"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/input_chip.dart", "hash": "14177be7a74b321668af2b9effa0f396"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/scheduler/ticker.dart", "hash": "c2e0fa3415ed461288b6e2aecf569919"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/radio_list_tile.dart", "hash": "cd7a7fd807697152dfdaeb3109e4f4f4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/term_glyph.dart", "hash": "1adcc56e3affffb23739c7c9d8a5fca0"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/_background_isolate_binary_messenger_io.dart", "hash": "991024814d51967a20be5851be93a8e3"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/restoration.dart", "hash": "04c713cbc0ac5e15c7978a2e91b81488"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/result/release_sink.dart", "hash": "e2f7d6fbeb362176a24cb422a6dd8193"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_string_codec.h", "hash": "5edcd4ae8b7d3acb2e56bf8bfad75d4d"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/gesture_settings.dart", "hash": "b5bd9d15c10929b4a63ea0df649e2d52"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/debug.dart", "hash": "9f05403438068337dd8f3433d2757535"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/extensions/paint_extension.dart", "hash": "738f81713ba9998f517c511158bce167"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/request.dart", "hash": "817e03d87771f133aacbdef89c1e6fc9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/lazy_chain.dart", "hash": "7ec268e37049e5c22e226c94df1776b3"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_view.h", "hash": "e27fc7ec7888b59ffac04a45e4461699"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/selectable_text.dart", "hash": "d7c9baf97f1348c00c56f8d64a3ce53a"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/layout_helper.dart", "hash": "1fd7c932679011d491315ff136d13822"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/src/json_document_transformer.dart", "hash": "eade7bbc9bacbd78204c7ffdde55ddbd"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/scrollbar.dart", "hash": "a2d1c7bec7b52901761f3d52a1ac02d5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/bottom_navigation_bar_item.dart", "hash": "900a13c9fcd73f4e8e3d069d76af6ffa"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/utils.dart", "hash": "8986177ba204a808c603c35260601cce"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/navigation_drawer.dart", "hash": "7755bff1bceea0db42330320ad10baad"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/lib/src/web_socket.dart", "hash": "4a734a1799a783075eeefe2296a393bf"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/gesture_detector.dart", "hash": "a103dff72cbe4ef64a02c37dbfdc752d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/bar_chart/bar_chart.dart", "hash": "0012d96ba5489f3c1b7473b9d0d30516"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/future_provider/auto_dispose.dart", "hash": "9ab6d0a38467598c8e1f332648cff545"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/_platform_io.dart", "hash": "bf6d84f8802d83e64fe83477c83752b4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/common.dart", "hash": "493b51476fc266d10a636f520fff01fc"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/bottom_navigation_bar_theme.dart", "hash": "307c2ee6ebc77b9995c2799e8e0bed81"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/geometry.dart", "hash": "9e353a749332f6cfdbe6f0d07ff17f5f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/ref.dart", "hash": "452cd5bd89dd73f555cc1ef42032e1f8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set_controller.dart", "hash": "f301af2d0392296f456363085becbf47"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/color_filter.dart", "hash": "bc3c12f9555c86aa11866996e60c0ec9"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/constants.dart", "hash": "2c6facdb1b63e687304c4b2852f6ef4c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/charcode.dart", "hash": "b80f25d51570eededff370f0c2b94c38"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/tab_controller.dart", "hash": "40587a28640d3c90ad2e52fdfbcd7520"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/text_selection_toolbar.dart", "hash": "2553e163ea84c7207282c18b5d9e14c1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/base.dart", "hash": "737fc999d5d26218c34c7423fe061f1e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/utils.dart", "hash": "caf148b76c44a3f0f1bd6055ddbb8f5e"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_method_codec.h", "hash": "ea928ca76b491e0a1313d69a25103ee3"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/animated_size.dart", "hash": "91d8303ca1ccc72eccc1ae636c7825ed"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/change_notifier_provider/auto_dispose.dart", "hash": "39d249bfedd0655b147701ff81de4fa1"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/switch_list_tile.dart", "hash": "d942bc7ece253c7918e1f60d35e233b0"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/text_selection_theme.dart", "hash": "798f76b8076951e542aad4221a45d480"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/lib/data/services/api_service.dart", "hash": "b42ef6695ef411dd4f027d6ef589f46a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_monochrome.dart", "hash": "66272a6751b167051ba879724cfe5749"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/shared_preferences_windows.dart", "hash": "2bc47cc0ce47761990162c3f08072016"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/sliding_segmented_control.dart", "hash": "2e074f4fb954a719546377c67cb54608"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/navigator.dart", "hash": "3ecea4d9c25299b0ea66c58256909437"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/ink_ripple.dart", "hash": "81fd3ef494f4443fb8565c98ba5a9ba2"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/flutter_logo.dart", "hash": "985cf5499dc6e521191985f55245a22c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/delegate/stream_consumer.dart", "hash": "987dfee9ed944d2007a00e521d4fbbe4"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/file.dart", "hash": "51ffa7b452686eecd94ed080a1da4275"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier.dart", "hash": "b60a2076a519fde0c9162319239b25eb"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_request.dart", "hash": "c738f304008379170f7306e4368d29dd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/characters.dart", "hash": "fa2a57b3b873fb7db4b8b961735e4ca3"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/date_picker_theme.dart", "hash": "34371da200382409d181bf9c3fcaefc7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes_dart_io.dart", "hash": "9df03a340058a4e7792cd68745a4320c"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_texture.h", "hash": "d1894650bbfe9ba65c6f0b195645a9a5"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/raw_keyboard_web.dart", "hash": "547eac441130505674f44bf786aee606"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/src/stream_channel_completer.dart", "hash": "a64b855dc42d91c681b48548816fec8b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/provider_base.dart", "hash": "ddbfb4de9e9dc40a09a6bfae74a41dd8"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/form.dart", "hash": "2be9783170f41208ab65361d7cb0ddc4"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/raw_keyboard.dart", "hash": "02dabe6a8cd832d69b4864626329ef30"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/constants.dart", "hash": "5aa32c5e6b696b66556b4f91bf5983a3"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/magnifier.dart", "hash": "b56cf23d49289ed9b2579fdc74f99c98"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/animated_icons/data/home_menu.g.dart", "hash": "11fc97acd20679368ae2eaa698c6f130"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/scaffold.dart", "hash": "498db9e29a08e6fdc8aee5eeb4d204ce"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/tooltip_theme.dart", "hash": "160e007517eb9af8299b242a217c6ff9"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/sliver_fill.dart", "hash": "123520ee3a48eebf4ba444e93436bb1a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/media_type.dart", "hash": "101ff6d49da9d3040faf0722153efee7"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/localizations.dart", "hash": "85e90b0b1f705d7db10d294017bcaf44"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512_fastsinks.dart", "hash": "7924bc2d95999b2767d9f34e6ac52f98"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/sliver_fixed_extent_list.dart", "hash": "2adcbf9fb509dd8fe8864a702db29043"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/shifted_box.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file.dart", "hash": "1026f587763defb6fb1eec88c2154a3d"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/local_platform.dart", "hash": "9cc2170ec43e47681be6cb2a313ba1b5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/boollist.dart", "hash": "206ef1a664f500f173416d5634d95c8b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/container.dart", "hash": "8597f18181783d905e40dc64f0c0555a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_directory.dart", "hash": "62da8696885bd25977675ac4f7f1aef9"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/result/result.dart", "hash": "c6e362e3e6b16241c22db67cbbd6b85b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_request.dart", "hash": "de670519e8f1f432d9f1a21fdd05b4b3"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/platform_menu_bar.dart", "hash": "44d59e37041b6305018f70012fef7d52"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/shortcuts.dart", "hash": "f1c0b135f35af022771e30409953e0f6"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/lib/presentation/widgets/tracking_controls.dart", "hash": "3ec66ae1354f476a50bb920528f9e5c2"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/shared_preferences_foundation.dart", "hash": "db8ef5ac4d806e72f7b356056cb50b1f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/single_child_scroll_view.dart", "hash": "e38cc213f0e4b4ed76471f4d70e20abe"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/box_fit.dart", "hash": "********************************"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/sphere.dart", "hash": "63473e31f03ea66a38affa41fd783752"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/list_tile.dart", "hash": "b3686e0781f3148d75a64ebb2bfef609"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/text_field.dart", "hash": "0f6f972f6232b9d18cf00a9fa432127b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/pie_chart/pie_chart.dart", "hash": "3dc4a56b0e2c0055de173c1f763e4127"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/scroll_notification_observer.dart", "hash": "a309d8ca64c3efb3ad74b742ffb0e1dd"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/scatter_chart/scatter_chart_helper.dart", "hash": "67743fd8f22abb05054245aae9a97a5f"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/text_selection.dart", "hash": "0c46b12a4e0301a199ef98521f0ed3ab"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/semantics/semantics.dart", "hash": "3e1bb909dcd21ccd8bdc03ba57bf02b2"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/feedback.dart", "hash": "c8f69577793923bfda707dcbb48a08b1"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/contrast_curve.dart", "hash": "9a12cf2a3549924510006db4651a1743"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/internal_style.dart", "hash": "974d0c452808a1c68d61285d0bd16b28"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/animation/animation_controller.dart", "hash": "0bb85eff209a2008dc5f47b2beda5bf3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/authentication_challenge.dart", "hash": "395f07418a28b12b0ed665f32270d702"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/text_editing_intents.dart", "hash": "ed582bff49cac60fb08ccee9ccc7c573"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/animation/animation.dart", "hash": "c8564aa311746f4047cd02e26ff4df75"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/animated_icons/data/menu_close.g.dart", "hash": "ef5fc00d685cd2a36c4de80e1c7e3a8f"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/common.dart", "hash": "1ab2b4b439160093cb35c9b0c739bc0b"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_controller.dart", "hash": "30b3454341d40c187ec21020db3a495b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/theme_data.dart", "hash": "f9646c35238459f46dd9d87783813f08"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/shadows.dart", "hash": "36fc598c656490ab430ca1be5fb909e8"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding.dart", "hash": "5f5c07df31f7d37780708976065ac8d3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface.dart", "hash": "5145b27b3db429f9f1da26cfe563bd02"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterable.dart", "hash": "67d16e841606c4e5355211fe15a2dbfd"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_pixel_buffer_texture.h", "hash": "fb73b65a52e89c14449cd8524058187b"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/ink_sparkle.dart", "hash": "9d2e926705e7e23b2e34aa022cf55324"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/result/value.dart", "hash": "bf3aeab9379cee97ddcc69d885a477f5"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/radar_chart/radar_chart_data.dart", "hash": "83858e10e2500cf984bc86a6fc987da3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_list.dart", "hash": "5b894ae18be3e2442a34288833184ca9"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/proxy_sliver.dart", "hash": "0201ee9c8aee2bb24db2c74b6c0cd485"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/sliver_padding.dart", "hash": "ddf1bde8f4b9706d5769690b7819e5d4"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/.dart_tool/flutter_build/dart_plugin_registrant.dart", "hash": "6280384419baa76c6d7be5298f6cb659"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/bottom_app_bar.dart", "hash": "23f5fb6033bd02c94d263d1ed41fb90e"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_event_channel.h", "hash": "defbac680fc3c78d98ef61319ff0dbb6"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/text_editing_delta.dart", "hash": "270de9c98f9c1284da0a6af9176ee1f9"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/keyboard_maps.g.dart", "hash": "2906cf9308cbed8eb54ab1638dd5f56e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/line_scanner.dart", "hash": "168bedc5b96bb6fea46c5b5aa43addd1"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/painting/image_cache.dart", "hash": "4895dd7c08da98c883cb21943f4ca4d2"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/container.dart", "hash": "f663757bacdc28f2692b30a293d75146"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/utils.dart", "hash": "d84ae47a3c688bd889f442426f39be3e"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/system_context_menu.dart", "hash": "a55ac84003178cdc783ca41a634500a8"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/action_buttons.dart", "hash": "aed826e965e4aa2fdb3466d39e33d824"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_provider.dart", "hash": "d28de61067df9bc3d509be84deec1140"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/internal/engine.version", "hash": "32c4a9b6e6aa250ffd4aba05be285558"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_method_call.h", "hash": "96e3a074c3f1f33e88ad3ccc60d6f9d3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/local.dart", "hash": "e81341d4c5ee8dc65f89ae4145cf2107"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/src/close_guarantee_channel.dart", "hash": "1536ff203bc26bdb4841b82c51187a6d"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/scheduler/debug.dart", "hash": "d72a4ddaf6162d8b897954e02b4a2a4c"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/viewport_offset.dart", "hash": "e45c87e4aadaebf7ba449f4c60929928"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/base/axis_chart/axis_chart_painter.dart", "hash": "d94472963d0ca8571b29f22b7da7fd1a"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/libflutter_linux_gtk.so", "hash": "c88521f8320e9580f9dd2269cc77cffa"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/grid_tile_bar.dart", "hash": "a340eddbf129cfd60e2c67db33c6003e"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/hardware_keyboard.dart", "hash": "a32174b6de983c1652638940e75aae6a"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/src/typed/stream_subscription.dart", "hash": "63190b810e77cfebf3de760baaf59832"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/scatter_chart/scatter_chart_renderer.dart", "hash": "16553f5a3ae11551df05ee7886a35916"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/snapshot_widget.dart", "hash": "883b210f4cc20daebdb2834dbe4a512c"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/exception.dart", "hash": "9011b30a404dec657806a780b55d0610"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/filled_button.dart", "hash": "3de98898d0fea89f0e609dcbf7b69783"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/form_section.dart", "hash": "cd995d0f309bf74d0bbe94eb1e4e8e81"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/tab_scaffold.dart", "hash": "9434ff8aa06e13d5981ed6ec15eceb64"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/binding.dart", "hash": "e40877daa15509fcbd3e465d246dbc09"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/page_scaffold.dart", "hash": "********************************"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/custom_paint.dart", "hash": "0f61d8c0c0870ae724b64f2f2af816bc"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/animation/animation_style.dart", "hash": "10505aa641207501d9a0759bf2d6515e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hmac.dart", "hash": "2b5fbc54f77ca9c1e5ac90eb3c242554"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/pop_scope.dart", "hash": "0ff55be19444856c892e701c475b20f6"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality.dart", "hash": "46e577ec532e21029e9cee153d7ca434"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/route.dart", "hash": "7b28ec35aed9cbc3319bf4c15d7b352a"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/drawer_theme.dart", "hash": "62b4a318d3ec0d03d3dc78b84cf0458a"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/services/predictive_back_event.dart", "hash": "16859f5e798cf33fc3c76a7a3dca05d7"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/table.dart", "hash": "1f437276972808bf4cf722440da1b231"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/pages.dart", "hash": "2c525c85cb323db613ddc5eba4b902d4"}, {"path": "/home/<USER>/.cache/flutter_sdk/bin/cache/artifacts/engine/linux-x64/flutter_linux/fl_method_channel.h", "hash": "5a5b80bcf4d72767c5fee74693a1140e"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/dropdown.dart", "hash": "726a60283ea6c3a38fbb1ea6139cb4f0"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/gestures/velocity_tracker.dart", "hash": "be0a77cf3f0463f3dacd09ec596d9002"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/chart/line_chart/line_chart_helper.dart", "hash": "ba86a82c34b62380d3852616e31389da"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/priority_queue.dart", "hash": "34a4d340931147322eaddc77fdc65c22"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters.dart", "hash": "99b4d15f76889687c07a41b43911cc39"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/case_insensitive_map.dart", "hash": "5893c7d3910e8924bd2dccc8837775c7"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_texture_registrar.h", "hash": "455c45888f33294a5054d8adacac5a2e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier.dart", "hash": "a67d1346ef152a92e983a9d7dc1a96fb"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_method_response.h", "hash": "63f9825adadb5fe17a5f3df6a8202686"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/foundation/annotations.dart", "hash": "b092b123c7d8046443429a9cd72baa9a"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/widgets/scroll_context.dart", "hash": "98f725d06ba20a1032cb8770d00d7fca"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/builders.dart", "hash": "dc1a141705a29df814f129c65b47b5d3"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/shared_preferences_android.dart", "hash": "30bffdef523e68fbb858483fd4340392"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/cupertino/icons.dart", "hash": "790dc5e1e0b058d13efbd42a3f46498e"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/utils.dart", "hash": "fab8d6d1b0e81315a3d78131394d31e6"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/rendering/sliver_tree.dart", "hash": "b33b1182e92dc3469db2563a33be2841"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2/lib/src/extensions/size_extension.dart", "hash": "3e30c6055f44db307b10e0f0bc89a5bb"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/flutter_linux/fl_json_message_codec.h", "hash": "1fa21d2586a24da4ab78ae2936263c7f"}, {"path": "/tmp/time_tracker_gui/time_tracker_gui/linux/flutter/ephemeral/libflutter_linux_gtk.so", "hash": "c88521f8320e9580f9dd2269cc77cffa"}, {"path": "/home/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_buffer.dart", "hash": "f64837679a1abb526e942b166db5c244"}, {"path": "/home/<USER>/.cache/flutter_sdk/packages/flutter/lib/src/material/tabs.dart", "hash": "3d7501e746aaa83cd9cc1b508d3f7ebe"}]}